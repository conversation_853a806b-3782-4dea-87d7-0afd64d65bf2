#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模块3：PU学习与特征选择
功能：负样本生成、特征重要性评估、特征选择
作者：AI Assistant
创建时间：2025年1月
"""

import os
import sys
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Union
from sklearn.ensemble import IsolationForest
from sklearn.svm import OneClassSVM
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix
import lightgbm as lgb
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    print("警告: SHAP库未安装，将跳过SHAP重要性计算")

from sklearn.inspection import permutation_importance
from sklearn.preprocessing import StandardScaler

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')
warnings.filterwarnings('ignore')

class PULearningFeatureSelector:
    """PU学习与特征选择器"""
    
    def __init__(self, data_file: str = 'processed_data.csv', pu_method: str = 'spy'):
        """
        初始化PU学习特征选择器
        
        Args:
            data_file: 处理后的数据文件路径
            pu_method: PU学习方法 ('spy', 'isolation_forest', 'one_class_svm')
        """
        self.data_file = data_file
        self.pu_method = pu_method
        self.df = None
        self.output_dir = 'output/03_feature_selection'
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 数据分割结果
        self.positive_samples = None
        self.unlabeled_samples = None
        self.negative_samples = None
        self.labeled_data = None
        
        # 特征选择结果
        self.selected_features = []
        self.feature_importance_results = {}
        
        # 可修改参数
        self.spy_ratio = 0.15  # Spy技术中正样本作为间谍的比例
        self.contamination = 0.1  # Isolation Forest污染率
        self.nu = 0.1  # One-Class SVM参数
        self.iv_threshold = 0.02  # IV值阈值，低于此值的特征将被删除
        self.top_features_count = 30  # 最终选择的特征数量
    
    def load_data(self) -> bool:
        """
        加载处理后的数据
        
        Returns:
            bool: 是否成功加载数据
        """
        try:
            print("正在加载处理后的数据...")
            self.df = pd.read_csv(self.data_file, encoding='utf-8')
            print(f"✓ 数据加载成功: {self.df.shape[0]:,} 行 × {self.df.shape[1]} 列")
            
            # 检查目标变量
            if '是否购买理财' not in self.df.columns:
                print("✗ 未找到目标变量 '是否购买理财'")
                return False
            
            # 统计正样本和未知样本
            target_counts = self.df['是否购买理财'].value_counts()
            print(f"目标变量分布:")
            for value, count in target_counts.items():
                print(f"  {value}: {count:,}")
            
            return True
        except Exception as e:
            print(f"✗ 数据加载失败: {str(e)}")
            return False
    
    def prepare_data_for_pu_learning(self):
        """准备PU学习数据"""
        print("\n" + "="*60)
        print("1. 准备PU学习数据")
        print("="*60)
        
        # 分离正样本和未知样本
        # 1是已购买理财的客户（正样本），"未知"是需要预测的样本
        # 注意：目标变量可能是字符串类型
        self.positive_samples = self.df[self.df['是否购买理财'] == '1'].copy()
        self.unlabeled_samples = self.df[self.df['是否购买理财'] == '未知'].copy()
        
        print(f"正样本数量: {len(self.positive_samples):,}")
        print(f"未知样本数量: {len(self.unlabeled_samples):,}")
        
        # 获取特征列（排除客户号和目标变量）
        feature_cols = [col for col in self.df.columns 
                       if col not in ['客户号', '是否购买理财']]
        
        print(f"特征数量: {len(feature_cols)}")
        
        return feature_cols
    
    def generate_negative_samples_spy(self, feature_cols: List[str]) -> pd.DataFrame:
        """
        使用Spy技术生成负样本
        
        Args:
            feature_cols: 特征列名列表
            
        Returns:
            pd.DataFrame: 生成的负样本
        """
        print(f"\n使用Spy技术生成负样本...")
        
        # 从正样本中随机选择一部分作为"间谍"
        spy_count = int(len(self.positive_samples) * self.spy_ratio)
        spy_indices = np.random.choice(len(self.positive_samples), spy_count, replace=False)
        spy_samples = self.positive_samples.iloc[spy_indices].copy()

        print(f"间谍样本数量: {len(spy_samples)}")

        # 将间谍样本混入未知样本中
        mixed_unlabeled = pd.concat([self.unlabeled_samples, spy_samples], ignore_index=True)

        # 训练一个二分类器来区分剩余的正样本和混合的未知样本
        # 使用布尔索引而不是drop方法
        remaining_mask = np.ones(len(self.positive_samples), dtype=bool)
        remaining_mask[spy_indices] = False
        remaining_positive = self.positive_samples[remaining_mask].copy()
        
        # 准备训练数据
        X_train = pd.concat([remaining_positive[feature_cols], mixed_unlabeled[feature_cols]], ignore_index=True)
        y_train = np.concatenate([np.ones(len(remaining_positive)), np.zeros(len(mixed_unlabeled))])
        
        # 训练LightGBM分类器
        train_data = lgb.Dataset(X_train, label=y_train)
        params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1
        }
        
        model = lgb.train(params, train_data, num_boost_round=100)
        
        # 对混合的未知样本进行预测
        predictions = model.predict(mixed_unlabeled[feature_cols])
        
        # 找到间谍样本的预测概率阈值
        spy_predictions = predictions[-len(spy_samples):]
        threshold = np.percentile(spy_predictions, 5)  # 可修改参数：阈值百分位数
        
        print(f"间谍样本预测概率阈值: {threshold:.4f}")
        
        # 选择预测概率低于阈值的未知样本作为负样本
        unlabeled_predictions = predictions[:-len(spy_samples)]
        negative_indices = np.where(unlabeled_predictions < threshold)[0]
        negative_samples = self.unlabeled_samples.iloc[negative_indices].copy()
        
        print(f"生成的负样本数量: {len(negative_samples)}")
        
        return negative_samples
    
    def generate_negative_samples_isolation_forest(self, feature_cols: List[str]) -> pd.DataFrame:
        """
        使用Isolation Forest生成负样本
        
        Args:
            feature_cols: 特征列名列表
            
        Returns:
            pd.DataFrame: 生成的负样本
        """
        print(f"\n使用Isolation Forest生成负样本...")
        
        # 在正样本上训练Isolation Forest
        iso_forest = IsolationForest(
            contamination=self.contamination,  # 可修改参数：污染率
            random_state=42,
            n_estimators=100
        )
        
        # 标准化特征
        scaler = StandardScaler()
        positive_features_scaled = scaler.fit_transform(self.positive_samples[feature_cols])
        
        # 训练模型
        iso_forest.fit(positive_features_scaled)
        
        # 对未知样本进行预测
        unlabeled_features_scaled = scaler.transform(self.unlabeled_samples[feature_cols])
        outlier_predictions = iso_forest.predict(unlabeled_features_scaled)
        
        # 选择被标记为异常值的样本作为负样本
        negative_indices = np.where(outlier_predictions == -1)[0]
        negative_samples = self.unlabeled_samples.iloc[negative_indices].copy()
        
        print(f"生成的负样本数量: {len(negative_samples)}")
        
        return negative_samples
    
    def generate_negative_samples_one_class_svm(self, feature_cols: List[str]) -> pd.DataFrame:
        """
        使用One-Class SVM生成负样本
        
        Args:
            feature_cols: 特征列名列表
            
        Returns:
            pd.DataFrame: 生成的负样本
        """
        print(f"\n使用One-Class SVM生成负样本...")
        
        # 在正样本上训练One-Class SVM
        oc_svm = OneClassSVM(
            nu=self.nu,  # 可修改参数：异常值比例的上界
            kernel='rbf',
            gamma='scale'
        )
        
        # 标准化特征
        scaler = StandardScaler()
        positive_features_scaled = scaler.fit_transform(self.positive_samples[feature_cols])
        
        # 训练模型
        oc_svm.fit(positive_features_scaled)
        
        # 对未知样本进行预测
        unlabeled_features_scaled = scaler.transform(self.unlabeled_samples[feature_cols])
        outlier_predictions = oc_svm.predict(unlabeled_features_scaled)
        
        # 选择被标记为异常值的样本作为负样本
        negative_indices = np.where(outlier_predictions == -1)[0]
        negative_samples = self.unlabeled_samples.iloc[negative_indices].copy()
        
        print(f"生成的负样本数量: {len(negative_samples)}")
        
        return negative_samples
    
    def generate_negative_samples(self, feature_cols: List[str]):
        """
        根据指定方法生成负样本
        
        Args:
            feature_cols: 特征列名列表
        """
        print("\n" + "="*60)
        print("2. 负样本生成")
        print("="*60)
        
        print(f"使用方法: {self.pu_method}")
        
        if self.pu_method == 'spy':
            self.negative_samples = self.generate_negative_samples_spy(feature_cols)
        elif self.pu_method == 'isolation_forest':
            self.negative_samples = self.generate_negative_samples_isolation_forest(feature_cols)
        elif self.pu_method == 'one_class_svm':
            self.negative_samples = self.generate_negative_samples_one_class_svm(feature_cols)
        else:
            raise ValueError(f"不支持的PU学习方法: {self.pu_method}")
        
        # 创建标注数据集
        positive_labeled = self.positive_samples.copy()
        positive_labeled['label'] = 1
        
        negative_labeled = self.negative_samples.copy()
        negative_labeled['label'] = 0
        
        self.labeled_data = pd.concat([positive_labeled, negative_labeled], ignore_index=True)
        
        print(f"\n标注数据集创建完成:")
        print(f"  正样本: {len(positive_labeled):,}")
        print(f"  负样本: {len(negative_labeled):,}")
        print(f"  总计: {len(self.labeled_data):,}")
        print(f"  正负样本比例: 1:{len(negative_labeled)/len(positive_labeled):.2f}")
    
    def analyze_positive_negative_distribution(self, feature_cols: List[str]):
        """分析正负样本在关键特征上的分布差异"""
        print("\n" + "="*60)
        print("3. 正负样本分布分析")
        print("="*60)
        
        # 选择几个重要特征进行分析
        important_features = []
        
        # 选择存款相关特征
        deposit_features = [col for col in feature_cols if '存款' in col or '余额' in col]
        important_features.extend(deposit_features[:3])
        
        # 选择交易相关特征
        transaction_features = [col for col in feature_cols if '交易' in col or '笔数' in col]
        important_features.extend(transaction_features[:3])
        
        # 如果特征不足，随机选择一些
        if len(important_features) < 6:
            remaining_features = [col for col in feature_cols if col not in important_features]
            important_features.extend(remaining_features[:6-len(important_features)])
        
        important_features = important_features[:6]  # 最多6个特征
        
        if not important_features:
            print("未找到可分析的特征")
            return
        
        # 绘制分布对比图
        self.plot_positive_negative_distributions(important_features)
        
        # 计算统计差异
        self.calculate_distribution_statistics(important_features)
    
    def plot_positive_negative_distributions(self, features: List[str]):
        """绘制正负样本特征分布对比图"""
        n_features = len(features)
        n_cols = 3
        n_rows = (n_features + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6*n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, feature in enumerate(features):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col]
            
            # 获取正负样本的特征值
            positive_values = self.positive_samples[feature].dropna()
            negative_values = self.negative_samples[feature].dropna()
            
            # 绘制直方图
            ax.hist(positive_values, bins=50, alpha=0.6, label='正样本', color='red', density=True)
            ax.hist(negative_values, bins=50, alpha=0.6, label='负样本', color='blue', density=True)
            
            ax.set_title(f'{feature}')
            ax.set_xlabel('数值')
            ax.set_ylabel('密度')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(n_features, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            axes[row, col].set_visible(False)
        
        plt.tight_layout()
        
        # 保存图片
        filename = f'{self.timestamp}_正负样本分布对比.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ 正负样本分布对比图已保存: {filepath}")
    
    def calculate_distribution_statistics(self, features: List[str]):
        """计算正负样本分布统计差异"""
        print(f"\n正负样本特征统计对比:")
        
        stats_comparison = []
        
        for feature in features:
            positive_values = self.positive_samples[feature].dropna()
            negative_values = self.negative_samples[feature].dropna()
            
            pos_mean = positive_values.mean()
            neg_mean = negative_values.mean()
            pos_std = positive_values.std()
            neg_std = negative_values.std()
            
            # 计算差异比例
            mean_diff_ratio = abs(pos_mean - neg_mean) / (abs(pos_mean) + 1e-8)
            
            stats_comparison.append({
                'feature': feature,
                'positive_mean': pos_mean,
                'negative_mean': neg_mean,
                'positive_std': pos_std,
                'negative_std': neg_std,
                'mean_diff_ratio': mean_diff_ratio
            })
            
            print(f"  {feature}:")
            print(f"    正样本均值: {pos_mean:.4f}, 标准差: {pos_std:.4f}")
            print(f"    负样本均值: {neg_mean:.4f}, 标准差: {neg_std:.4f}")
            print(f"    均值差异比例: {mean_diff_ratio:.4f}")
        
        # 保存统计结果
        stats_df = pd.DataFrame(stats_comparison)
        stats_file = os.path.join(self.output_dir, f'{self.timestamp}_正负样本统计对比.csv')
        stats_df.to_csv(stats_file, index=False, encoding='utf-8-sig')
        print(f"\n✓ 统计对比结果已保存: {stats_file}")

    def calculate_iv_values(self, feature_cols: List[str]) -> Dict[str, float]:
        """
        计算特征的IV值（Information Value）

        Args:
            feature_cols: 特征列名列表

        Returns:
            Dict[str, float]: 特征名到IV值的映射
        """
        print("\n" + "="*60)
        print("4. 特征IV值计算（粗筛）")
        print("="*60)

        iv_values = {}

        for feature in feature_cols:
            try:
                # 获取特征值和标签
                feature_values = self.labeled_data[feature]
                labels = self.labeled_data['label']

                # 处理缺失值
                valid_mask = ~feature_values.isnull()
                feature_values = feature_values[valid_mask]
                labels = labels[valid_mask]

                if len(feature_values) == 0:
                    iv_values[feature] = 0.0
                    continue

                # 对连续变量进行分箱
                if feature_values.dtype in ['float64', 'int64']:
                    # 使用分位数分箱
                    try:
                        bins = pd.qcut(feature_values, q=10, duplicates='drop')
                    except:
                        # 如果分位数分箱失败，使用等宽分箱
                        bins = pd.cut(feature_values, bins=10, duplicates='drop')
                else:
                    # 分类变量直接使用原值
                    bins = feature_values

                # 计算每个分箱的正负样本比例
                crosstab = pd.crosstab(bins, labels)

                if crosstab.shape[1] < 2:
                    iv_values[feature] = 0.0
                    continue

                # 计算IV值
                total_pos = crosstab[1].sum()
                total_neg = crosstab[0].sum()

                if total_pos == 0 or total_neg == 0:
                    iv_values[feature] = 0.0
                    continue

                iv = 0.0
                for idx in crosstab.index:
                    pos_count = crosstab.loc[idx, 1]
                    neg_count = crosstab.loc[idx, 0]

                    if pos_count == 0 or neg_count == 0:
                        continue

                    pos_rate = pos_count / total_pos
                    neg_rate = neg_count / total_neg

                    if pos_rate > 0 and neg_rate > 0:
                        woe = np.log(pos_rate / neg_rate)
                        iv += (pos_rate - neg_rate) * woe

                iv_values[feature] = iv

            except Exception as e:
                print(f"计算 {feature} 的IV值时出错: {str(e)}")
                iv_values[feature] = 0.0

        # 按IV值排序
        sorted_iv = sorted(iv_values.items(), key=lambda x: x[1], reverse=True)

        print(f"IV值计算完成，前20个特征:")
        for i, (feature, iv) in enumerate(sorted_iv[:20], 1):
            print(f"  {i:2d}. {feature}: {iv:.4f}")

        # 筛选IV值大于阈值的特征
        selected_features = [feature for feature, iv in iv_values.items()
                           if iv > self.iv_threshold]

        print(f"\nIV值筛选结果:")
        print(f"  阈值: {self.iv_threshold}")
        print(f"  筛选前特征数: {len(feature_cols)}")
        print(f"  筛选后特征数: {len(selected_features)}")
        print(f"  删除特征数: {len(feature_cols) - len(selected_features)}")

        return iv_values, selected_features

    def calculate_feature_importance_lgb(self, feature_cols: List[str]) -> Dict[str, float]:
        """
        使用LightGBM计算特征重要性

        Args:
            feature_cols: 特征列名列表

        Returns:
            Dict[str, float]: 特征重要性字典
        """
        print(f"\n使用LightGBM计算特征重要性...")

        # 准备数据
        X = self.labeled_data[feature_cols]
        y = self.labeled_data['label']

        # 训练LightGBM模型
        train_data = lgb.Dataset(X, label=y)
        params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1
        }

        model = lgb.train(params, train_data, num_boost_round=100)

        # 获取特征重要性
        importance_gain = model.feature_importance(importance_type='gain')
        importance_split = model.feature_importance(importance_type='split')

        # 归一化重要性分数
        importance_gain_norm = importance_gain / importance_gain.sum()
        importance_split_norm = importance_split / importance_split.sum()

        # 组合两种重要性分数
        combined_importance = (importance_gain_norm + importance_split_norm) / 2

        feature_importance = dict(zip(feature_cols, combined_importance))

        print(f"✓ LightGBM特征重要性计算完成")

        return feature_importance

    def calculate_shap_importance(self, feature_cols: List[str]) -> Dict[str, float]:
        """
        使用SHAP计算特征重要性

        Args:
            feature_cols: 特征列名列表

        Returns:
            Dict[str, float]: SHAP重要性字典
        """
        if not SHAP_AVAILABLE:
            print(f"\n跳过SHAP重要性计算（SHAP库未安装）")
            # 返回均匀分布的重要性作为占位符
            uniform_importance = 1.0 / len(feature_cols)
            return {feature: uniform_importance for feature in feature_cols}

        print(f"\n使用SHAP计算特征重要性...")

        # 准备数据
        X = self.labeled_data[feature_cols]
        y = self.labeled_data['label']

        # 训练LightGBM模型
        train_data = lgb.Dataset(X, label=y)
        params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1
        }

        model = lgb.train(params, train_data, num_boost_round=100, verbose_eval=False)

        # 计算SHAP值（使用样本子集以提高速度）
        sample_size = min(1000, len(X))  # 可修改参数：SHAP计算样本数
        sample_indices = np.random.choice(len(X), sample_size, replace=False)
        X_sample = X.iloc[sample_indices]

        # 创建SHAP解释器
        explainer = shap.TreeExplainer(model)
        shap_values = explainer.shap_values(X_sample)

        # 计算每个特征的平均绝对SHAP值
        if isinstance(shap_values, list):
            shap_values = shap_values[1]  # 对于二分类，取正类的SHAP值

        mean_abs_shap = np.mean(np.abs(shap_values), axis=0)

        # 归一化
        mean_abs_shap_norm = mean_abs_shap / mean_abs_shap.sum()

        shap_importance = dict(zip(feature_cols, mean_abs_shap_norm))

        print(f"✓ SHAP特征重要性计算完成")

        return shap_importance

    def calculate_permutation_importance(self, feature_cols: List[str]) -> Dict[str, float]:
        """
        使用排列重要性计算特征重要性

        Args:
            feature_cols: 特征列名列表

        Returns:
            Dict[str, float]: 排列重要性字典
        """
        print(f"\n使用排列重要性计算特征重要性...")

        # 准备数据
        X = self.labeled_data[feature_cols]
        y = self.labeled_data['label']

        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )

        # 训练LightGBM模型
        train_data = lgb.Dataset(X_train, label=y_train)
        params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1
        }

        model = lgb.train(params, train_data, num_boost_round=100, verbose_eval=False)

        # 创建预测函数
        def predict_func(X):
            return model.predict(X)

        # 计算排列重要性
        perm_importance = permutation_importance(
            predict_func, X_test, y_test,
            n_repeats=5,  # 可修改参数：重复次数
            random_state=42,
            scoring='f1'
        )

        # 归一化重要性分数
        importance_mean = perm_importance.importances_mean
        importance_norm = importance_mean / importance_mean.sum()

        permutation_importance_dict = dict(zip(feature_cols, importance_norm))

        print(f"✓ 排列重要性计算完成")

        return permutation_importance_dict

    def perform_feature_selection(self, iv_selected_features: List[str]):
        """
        执行特征选择的精选步骤

        Args:
            iv_selected_features: IV值筛选后的特征列表
        """
        print("\n" + "="*60)
        print("5. 特征重要性评估（精选）")
        print("="*60)

        # 计算三种特征重要性
        lgb_importance = self.calculate_feature_importance_lgb(iv_selected_features)
        shap_importance = self.calculate_shap_importance(iv_selected_features)
        perm_importance = self.calculate_permutation_importance(iv_selected_features)

        # 存储结果
        self.feature_importance_results = {
            'lgb_importance': lgb_importance,
            'shap_importance': shap_importance,
            'permutation_importance': perm_importance
        }

        # 综合排名
        self.combine_feature_rankings(iv_selected_features)

        # 可视化特征重要性
        self.plot_feature_importance()

    def combine_feature_rankings(self, features: List[str]):
        """
        综合多种特征重要性排名

        Args:
            features: 特征列表
        """
        print("\n" + "="*60)
        print("6. 综合特征排名")
        print("="*60)

        # 为每种方法计算排名
        lgb_ranks = self.get_feature_ranks(self.feature_importance_results['lgb_importance'])
        shap_ranks = self.get_feature_ranks(self.feature_importance_results['shap_importance'])
        perm_ranks = self.get_feature_ranks(self.feature_importance_results['permutation_importance'])

        # 计算综合排名（排名越小越重要）
        combined_ranks = {}
        for feature in features:
            # 使用加权平均排名
            combined_rank = (
                lgb_ranks.get(feature, len(features)) * 0.4 +  # 可修改参数：LightGBM权重
                shap_ranks.get(feature, len(features)) * 0.4 +  # 可修改参数：SHAP权重
                perm_ranks.get(feature, len(features)) * 0.2   # 可修改参数：排列重要性权重
            )
            combined_ranks[feature] = combined_rank

        # 按综合排名排序
        sorted_features = sorted(combined_ranks.items(), key=lambda x: x[1])

        # 选择前N个特征
        self.selected_features = [feature for feature, rank in sorted_features[:self.top_features_count]]

        print(f"综合排名前{self.top_features_count}个特征:")
        for i, (feature, rank) in enumerate(sorted_features[:self.top_features_count], 1):
            lgb_score = self.feature_importance_results['lgb_importance'].get(feature, 0)
            shap_score = self.feature_importance_results['shap_importance'].get(feature, 0)
            perm_score = self.feature_importance_results['permutation_importance'].get(feature, 0)

            print(f"  {i:2d}. {feature}")
            print(f"      综合排名: {rank:.2f}")
            print(f"      LGB: {lgb_score:.4f}, SHAP: {shap_score:.4f}, PERM: {perm_score:.4f}")

    def get_feature_ranks(self, importance_dict: Dict[str, float]) -> Dict[str, int]:
        """
        将重要性分数转换为排名

        Args:
            importance_dict: 特征重要性字典

        Returns:
            Dict[str, int]: 特征排名字典
        """
        sorted_features = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)
        ranks = {feature: rank + 1 for rank, (feature, score) in enumerate(sorted_features)}
        return ranks

    def plot_feature_importance(self):
        """绘制特征重要性对比图"""
        print(f"\n绘制特征重要性对比图...")

        # 选择前20个特征进行可视化
        top_features = self.selected_features[:20]

        # 准备数据
        lgb_scores = [self.feature_importance_results['lgb_importance'].get(f, 0) for f in top_features]
        shap_scores = [self.feature_importance_results['shap_importance'].get(f, 0) for f in top_features]
        perm_scores = [self.feature_importance_results['permutation_importance'].get(f, 0) for f in top_features]

        # 创建图表
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 18))

        # LightGBM重要性
        y_pos = np.arange(len(top_features))
        ax1.barh(y_pos, lgb_scores, alpha=0.8, color='skyblue')
        ax1.set_yticks(y_pos)
        ax1.set_yticklabels(top_features)
        ax1.set_xlabel('重要性分数')
        ax1.set_title('LightGBM特征重要性 - 前20个特征')
        ax1.grid(True, alpha=0.3)

        # SHAP重要性
        ax2.barh(y_pos, shap_scores, alpha=0.8, color='lightcoral')
        ax2.set_yticks(y_pos)
        ax2.set_yticklabels(top_features)
        ax2.set_xlabel('重要性分数')
        ax2.set_title('SHAP特征重要性 - 前20个特征')
        ax2.grid(True, alpha=0.3)

        # 排列重要性
        ax3.barh(y_pos, perm_scores, alpha=0.8, color='lightgreen')
        ax3.set_yticks(y_pos)
        ax3.set_yticklabels(top_features)
        ax3.set_xlabel('重要性分数')
        ax3.set_title('排列重要性 - 前20个特征')
        ax3.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_特征重要性对比.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✓ 特征重要性对比图已保存: {filepath}")

    def save_results(self):
        """保存特征选择结果"""
        print("\n" + "="*60)
        print("7. 保存结果")
        print("="*60)

        # 保存标注样本数据（不带时间戳）
        labeled_file = 'labeled_samples.csv'
        self.labeled_data.to_csv(labeled_file, index=False, encoding='utf-8-sig')
        print(f"✓ 标注样本数据已保存: {labeled_file}")

        # 保存备份文件（带时间戳）
        backup_file = f'labeled_samples_backup_{self.timestamp}.csv'
        self.labeled_data.to_csv(backup_file, index=False, encoding='utf-8-sig')
        print(f"✓ 备份标注样本数据已保存: {backup_file}")

        # 保存最终特征列表（不带时间戳）
        import json
        features_file = 'final_features.json'
        features_data = {
            'selected_features': self.selected_features,
            'selection_method': 'combined_ranking',
            'pu_method': self.pu_method,
            'feature_count': len(self.selected_features),
            'timestamp': self.timestamp
        }

        with open(features_file, 'w', encoding='utf-8') as f:
            json.dump(features_data, f, ensure_ascii=False, indent=2)
        print(f"✓ 最终特征列表已保存: {features_file}")

        # 保存备份文件（带时间戳）
        backup_features_file = f'final_features_backup_{self.timestamp}.json'
        with open(backup_features_file, 'w', encoding='utf-8') as f:
            json.dump(features_data, f, ensure_ascii=False, indent=2)
        print(f"✓ 备份特征列表已保存: {backup_features_file}")

        # 保存详细的特征重要性结果
        detailed_results = {
            'pu_method': self.pu_method,
            'positive_samples_count': len(self.positive_samples),
            'negative_samples_count': len(self.negative_samples),
            'selected_features': self.selected_features,
            'feature_importance_results': self.feature_importance_results,
            'parameters': {
                'spy_ratio': self.spy_ratio,
                'contamination': self.contamination,
                'nu': self.nu,
                'iv_threshold': self.iv_threshold,
                'top_features_count': self.top_features_count
            }
        }

        detailed_file = os.path.join(self.output_dir, f'{self.timestamp}_特征选择详细结果.json')
        with open(detailed_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, ensure_ascii=False, indent=2, default=str)
        print(f"✓ 详细特征选择结果已保存: {detailed_file}")

    def generate_report(self):
        """生成特征选择报告"""
        print("\n" + "="*60)
        print("8. 生成分析报告")
        print("="*60)

        report_content = f"""# PU学习与特征选择报告

## 报告信息
- **生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **数据文件**: {self.data_file}
- **PU学习方法**: {self.pu_method}

## 1. 数据概况

### 原始数据
- **总样本数**: {len(self.df):,}
- **正样本数**: {len(self.positive_samples):,}
- **未知样本数**: {len(self.unlabeled_samples):,}
- **原始特征数**: {len([col for col in self.df.columns if col not in ['客户号', '是否购买理财']])}

### 标注数据
- **正样本数**: {len(self.positive_samples):,}
- **负样本数**: {len(self.negative_samples):,}
- **正负样本比例**: 1:{len(self.negative_samples)/len(self.positive_samples):.2f}

## 2. PU学习方法

### {self.pu_method.upper()}方法
"""

        if self.pu_method == 'spy':
            report_content += f"""
- **间谍比例**: {self.spy_ratio}
- **方法描述**: 从正样本中随机选择{self.spy_ratio*100:.1f}%作为间谍，混入未知样本中训练分类器
"""
        elif self.pu_method == 'isolation_forest':
            report_content += f"""
- **污染率**: {self.contamination}
- **方法描述**: 使用孤立森林在正样本上训练，识别未知样本中的异常值作为负样本
"""
        elif self.pu_method == 'one_class_svm':
            report_content += f"""
- **Nu参数**: {self.nu}
- **方法描述**: 使用单类支持向量机在正样本上训练，识别未知样本中的异常值作为负样本
"""

        report_content += f"""
## 3. 特征选择流程

### 第一步：IV值粗筛
- **IV阈值**: {self.iv_threshold}
- **筛选前特征数**: {len([col for col in self.df.columns if col not in ['客户号', '是否购买理财']])}
- **筛选后特征数**: 通过IV值筛选

### 第二步：多方法特征重要性评估
1. **LightGBM特征重要性**: 基于梯度提升决策树的特征重要性
2. **SHAP值**: 基于博弈论的特征贡献度分析
3. **排列重要性**: 基于特征打乱后性能下降的重要性评估

### 第三步：综合排名
- **最终特征数**: {len(self.selected_features)}
- **排名策略**: 加权综合排名（LGB:40%, SHAP:40%, PERM:20%）

## 4. 最终选择的特征

### 前20个重要特征
"""

        for i, feature in enumerate(self.selected_features[:20], 1):
            lgb_score = self.feature_importance_results['lgb_importance'].get(feature, 0)
            shap_score = self.feature_importance_results['shap_importance'].get(feature, 0)
            perm_score = self.feature_importance_results['permutation_importance'].get(feature, 0)

            report_content += f"""
{i:2d}. **{feature}**
   - LightGBM: {lgb_score:.4f}
   - SHAP: {shap_score:.4f}
   - 排列重要性: {perm_score:.4f}
"""

        report_content += f"""
## 5. 关键发现

### 数据质量
- PU学习成功生成了{len(self.negative_samples):,}个负样本
- 正负样本在关键特征上表现出明显差异
- 特征选择有效降低了维度，提高了模型可解释性

### 特征特点
- 存款相关特征在区分正负样本中起重要作用
- 交易行为特征显示出较强的预测能力
- 业务使用情况特征有助于客户分类

## 6. 输出文件

### 主要文件
- `labeled_samples.csv`: 标注后的训练数据
- `final_features.json`: 最终选择的特征列表

### 分析文件
- 正负样本分布对比图
- 特征重要性对比图
- 统计对比结果CSV

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        # 保存报告
        report_file = os.path.join(self.output_dir, f'{self.timestamp}_PU学习特征选择报告.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✓ PU学习特征选择报告已保存: {report_file}")

    def run_pu_learning_feature_selection(self):
        """运行完整的PU学习与特征选择流程"""
        print("开始PU学习与特征选择...")
        print("="*80)

        # 1. 加载数据
        if not self.load_data():
            return False

        # 2. 准备PU学习数据
        feature_cols = self.prepare_data_for_pu_learning()

        # 3. 生成负样本
        self.generate_negative_samples(feature_cols)

        # 4. 分析正负样本分布
        self.analyze_positive_negative_distribution(feature_cols)

        # 5. IV值粗筛
        iv_values, iv_selected_features = self.calculate_iv_values(feature_cols)

        # 6. 特征重要性评估与选择
        self.perform_feature_selection(iv_selected_features)

        # 7. 保存结果
        self.save_results()

        # 8. 生成报告
        self.generate_report()

        print("\n" + "="*80)
        print("PU学习与特征选择完成！")
        print(f"最终选择特征数: {len(self.selected_features)}")
        print(f"标注样本数: {len(self.labeled_data):,}")
        print(f"所有结果已保存到: {self.output_dir}")
        print("="*80)

        return True

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='PU学习与特征选择')
    parser.add_argument('--pu_method', type=str, default='spy',
                       choices=['spy', 'isolation_forest', 'one_class_svm'],
                       help='PU学习方法')

    args = parser.parse_args()

    # 创建PU学习特征选择器
    selector = PULearningFeatureSelector(pu_method=args.pu_method)

    # 运行特征选择
    success = selector.run_pu_learning_feature_selection()

    if success:
        print("\n✓ 模块3执行成功")
        return 0
    else:
        print("\n✗ 模块3执行失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
