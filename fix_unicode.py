#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复Unicode字符问题
将所有模块中的Unicode字符替换为ASCII字符
"""

import os
import re

def fix_unicode_in_file(filepath):
    """修复文件中的Unicode字符"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换Unicode字符
        content = content.replace('✓', '[OK]')
        content = content.replace('✗', '[ERROR]')
        content = content.replace('⚠', '[WARNING]')
        content = content.replace('🎉', '[SUCCESS]')
        content = content.replace('×', 'x')
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"修复完成: {filepath}")
        return True
    except Exception as e:
        print(f"修复失败 {filepath}: {str(e)}")
        return False

def main():
    """主函数"""
    # 需要修复的文件列表
    files_to_fix = [
        '01_data_exploration.py',
        '02_data_preprocessing.py',
        '03_pu_learning_feature_selection.py',
        '04_customer_prediction.py',
        '05_customer_clustering.py',
        '06_results_dashboard.py'
    ]
    
    print("开始修复Unicode字符问题...")
    
    success_count = 0
    for filepath in files_to_fix:
        if os.path.exists(filepath):
            if fix_unicode_in_file(filepath):
                success_count += 1
        else:
            print(f"文件不存在: {filepath}")
    
    print(f"\n修复完成! 成功修复 {success_count}/{len(files_to_fix)} 个文件")

if __name__ == "__main__":
    main()
