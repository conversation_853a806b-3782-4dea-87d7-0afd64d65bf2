#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模块5：客户聚类分析
功能：K-Means和DBSCAN双算法聚类、客户画像分析、高潜力客户识别
作者：AI Assistant
创建时间：2025年1月
"""

import os
import sys
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Union
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score, adjusted_rand_score
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import itertools

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')
warnings.filterwarnings('ignore')

class CustomerClusterer:
    """客户聚类分析器"""
    
    def __init__(self, clustering_method: str = 'both'):
        """
        初始化客户聚类器
        
        Args:
            clustering_method: 聚类方法 ('kmeans', 'dbscan', 'both')
        """
        self.clustering_method = clustering_method
        self.df = None
        self.selected_features = []
        self.X_scaled = None
        self.scaler = None
        
        self.output_dir = 'output/05_clustering'
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 聚类结果存储
        self.kmeans_results = {}
        self.dbscan_results = {}
        self.clustering_results = None
        
        # 可修改参数
        self.kmeans_k_range = range(2, 10)  # K-Means的K值范围（减少以提高速度）
        self.dbscan_eps_range = np.arange(0.1, 2.0, 0.1)  # DBSCAN的eps范围
        self.dbscan_min_samples_range = range(5, 21, 5)  # DBSCAN的min_samples范围
        self.random_state = 42
    
    def load_data(self) -> bool:
        """
        加载处理后的数据和特征列表
        
        Returns:
            bool: 是否成功加载数据
        """
        try:
            print("正在加载数据...")
            
            # 加载处理后的完整数据
            self.df = pd.read_csv('processed_data.csv', encoding='utf-8')
            print(f"✓ 数据加载成功: {self.df.shape[0]:,} 行 × {self.df.shape[1]} 列")
            
            # 加载特征列表
            with open('final_features.json', 'r', encoding='utf-8') as f:
                features_data = json.load(f)
                self.selected_features = features_data['selected_features']
            print(f"✓ 特征列表加载成功: {len(self.selected_features)} 个特征")
            
            # 统计数据分布
            target_counts = self.df['是否购买理财'].value_counts()
            print(f"数据分布:")
            for value, count in target_counts.items():
                print(f"  {value}: {count:,}")
            
            return True
        except Exception as e:
            print(f"✗ 数据加载失败: {str(e)}")
            return False
    
    def prepare_clustering_data(self):
        """准备聚类数据"""
        print("\n" + "="*60)
        print("1. 准备聚类数据")
        print("="*60)
        
        # 提取特征数据
        X = self.df[self.selected_features].values.astype(float)
        
        # 处理缺失值
        missing_count = np.isnan(X).sum()
        if missing_count > 0:
            print(f"处理 {missing_count} 个缺失值...")
            X = np.nan_to_num(X, nan=0.0)
        
        print(f"特征矩阵形状: {X.shape}")
        
        # 数据标准化
        print(f"进行数据标准化...")
        self.scaler = StandardScaler()
        self.X_scaled = self.scaler.fit_transform(X)
        
        print(f"✓ 数据准备完成")
        print(f"  标准化后特征均值: {self.X_scaled.mean(axis=0)[:5]}...")
        print(f"  标准化后特征标准差: {self.X_scaled.std(axis=0)[:5]}...")
    
    def perform_kmeans_clustering(self):
        """执行K-Means聚类"""
        print("\n" + "="*60)
        print("2. K-Means聚类分析")
        print("="*60)

        # 寻找最优K值
        print(f"寻找最优K值 (范围: {min(self.kmeans_k_range)}-{max(self.kmeans_k_range)})...")

        # 对于大数据集，使用采样来计算轮廓系数
        sample_size = min(10000, len(self.X_scaled))
        if len(self.X_scaled) > sample_size:
            print(f"  数据量较大，使用 {sample_size:,} 个样本计算轮廓系数...")
            sample_indices = np.random.choice(len(self.X_scaled), sample_size, replace=False)
            X_sample = self.X_scaled[sample_indices]
        else:
            X_sample = self.X_scaled
            sample_indices = np.arange(len(self.X_scaled))

        inertias = []
        silhouette_scores = []

        for k in self.kmeans_k_range:
            print(f"  测试 K={k}...")

            kmeans = KMeans(n_clusters=k, random_state=self.random_state, n_init=10)
            cluster_labels = kmeans.fit_predict(self.X_scaled)

            inertias.append(kmeans.inertia_)

            # 计算轮廓系数（使用采样数据）
            if k > 1:
                sample_labels = cluster_labels[sample_indices]
                silhouette_avg = silhouette_score(X_sample, sample_labels)
                silhouette_scores.append(silhouette_avg)
                print(f"    轮廓系数: {silhouette_avg:.4f}")
            else:
                silhouette_scores.append(0)
        
        # 选择最优K值（轮廓系数最高）
        best_k = self.kmeans_k_range[np.argmax(silhouette_scores)]
        best_silhouette = max(silhouette_scores)
        
        print(f"\n最优K值: {best_k} (轮廓系数: {best_silhouette:.4f})")
        
        # 使用最优K值进行最终聚类
        print(f"使用K={best_k}进行最终聚类...")
        final_kmeans = KMeans(n_clusters=best_k, random_state=self.random_state, n_init=10)
        kmeans_labels = final_kmeans.fit_predict(self.X_scaled)
        
        # 存储K-Means结果
        self.kmeans_results = {
            'model': final_kmeans,
            'labels': kmeans_labels,
            'best_k': best_k,
            'best_silhouette': best_silhouette,
            'inertias': inertias,
            'silhouette_scores': silhouette_scores,
            'cluster_centers': final_kmeans.cluster_centers_
        }
        
        # 统计聚类结果
        unique_labels, counts = np.unique(kmeans_labels, return_counts=True)
        print(f"\nK-Means聚类结果:")
        for label, count in zip(unique_labels, counts):
            print(f"  簇 {label}: {count:,} 个客户 ({count/len(kmeans_labels)*100:.2f}%)")
        
        print(f"✓ K-Means聚类完成")
    
    def perform_dbscan_clustering(self):
        """执行DBSCAN聚类"""
        print("\n" + "="*60)
        print("3. DBSCAN聚类分析")
        print("="*60)
        
        print(f"寻找最优参数...")
        print(f"  eps范围: {self.dbscan_eps_range[0]:.1f}-{self.dbscan_eps_range[-1]:.1f}")
        print(f"  min_samples范围: {min(self.dbscan_min_samples_range)}-{max(self.dbscan_min_samples_range)}")
        
        best_params = None
        best_silhouette = -1
        best_labels = None
        best_model = None
        
        # 网格搜索最优参数
        param_combinations = list(itertools.product(self.dbscan_eps_range, self.dbscan_min_samples_range))
        
        print(f"测试 {len(param_combinations)} 种参数组合...")
        
        for i, (eps, min_samples) in enumerate(param_combinations):
            if (i + 1) % 20 == 0:
                print(f"  进度: {i+1}/{len(param_combinations)}")
            
            dbscan = DBSCAN(eps=eps, min_samples=min_samples)
            cluster_labels = dbscan.fit_predict(self.X_scaled)
            
            # 检查聚类结果
            unique_labels = np.unique(cluster_labels)
            n_clusters = len(unique_labels) - (1 if -1 in cluster_labels else 0)
            
            # 至少要有2个簇且噪声点不能太多
            if n_clusters >= 2:
                noise_ratio = np.sum(cluster_labels == -1) / len(cluster_labels)
                if noise_ratio < 0.5:  # 噪声点比例不超过50%
                    try:
                        silhouette_avg = silhouette_score(self.X_scaled, cluster_labels)
                        if silhouette_avg > best_silhouette:
                            best_silhouette = silhouette_avg
                            best_params = (eps, min_samples)
                            best_labels = cluster_labels.copy()
                            best_model = dbscan
                    except:
                        continue
        
        if best_params is None:
            print("✗ 未找到合适的DBSCAN参数，使用默认参数")
            # 使用默认参数
            dbscan = DBSCAN(eps=0.5, min_samples=5)
            best_labels = dbscan.fit_predict(self.X_scaled)
            best_params = (0.5, 5)
            best_silhouette = 0
            best_model = dbscan
        
        print(f"\n最优参数: eps={best_params[0]:.2f}, min_samples={best_params[1]}")
        print(f"最优轮廓系数: {best_silhouette:.4f}")
        
        # 存储DBSCAN结果
        self.dbscan_results = {
            'model': best_model,
            'labels': best_labels,
            'best_eps': best_params[0],
            'best_min_samples': best_params[1],
            'best_silhouette': best_silhouette
        }
        
        # 统计聚类结果
        unique_labels, counts = np.unique(best_labels, return_counts=True)
        n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
        noise_count = np.sum(best_labels == -1)
        
        print(f"\nDBSCAN聚类结果:")
        print(f"  簇数量: {n_clusters}")
        print(f"  噪声点: {noise_count:,} 个 ({noise_count/len(best_labels)*100:.2f}%)")
        
        for label, count in zip(unique_labels, counts):
            if label == -1:
                print(f"  噪声: {count:,} 个客户 ({count/len(best_labels)*100:.2f}%)")
            else:
                print(f"  簇 {label}: {count:,} 个客户 ({count/len(best_labels)*100:.2f}%)")
        
        print(f"✓ DBSCAN聚类完成")
    
    def compare_clustering_results(self):
        """对比聚类结果"""
        print("\n" + "="*60)
        print("4. 聚类结果对比")
        print("="*60)
        
        comparison_results = {}
        
        if self.clustering_method in ['kmeans', 'both'] and self.kmeans_results:
            kmeans_labels = self.kmeans_results['labels']
            kmeans_silhouette = self.kmeans_results['best_silhouette']
            kmeans_clusters = len(np.unique(kmeans_labels))
            
            comparison_results['kmeans'] = {
                'n_clusters': kmeans_clusters,
                'silhouette_score': kmeans_silhouette,
                'noise_ratio': 0.0  # K-Means没有噪声点
            }
            
            print(f"K-Means结果:")
            print(f"  簇数量: {kmeans_clusters}")
            print(f"  轮廓系数: {kmeans_silhouette:.4f}")
        
        if self.clustering_method in ['dbscan', 'both'] and self.dbscan_results:
            dbscan_labels = self.dbscan_results['labels']
            dbscan_silhouette = self.dbscan_results['best_silhouette']
            dbscan_clusters = len(np.unique(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)
            noise_ratio = np.sum(dbscan_labels == -1) / len(dbscan_labels)
            
            comparison_results['dbscan'] = {
                'n_clusters': dbscan_clusters,
                'silhouette_score': dbscan_silhouette,
                'noise_ratio': noise_ratio
            }
            
            print(f"\nDBSCAN结果:")
            print(f"  簇数量: {dbscan_clusters}")
            print(f"  轮廓系数: {dbscan_silhouette:.4f}")
            print(f"  噪声比例: {noise_ratio*100:.2f}%")
        
        # 如果两种方法都执行了，计算一致性
        if self.clustering_method == 'both' and self.kmeans_results and self.dbscan_results:
            # 计算调整兰德指数
            ari = adjusted_rand_score(kmeans_labels, dbscan_labels)
            comparison_results['consistency'] = {
                'adjusted_rand_index': ari
            }
            print(f"\n聚类一致性:")
            print(f"  调整兰德指数: {ari:.4f}")
        
        self.comparison_results = comparison_results
        
        print(f"✓ 聚类结果对比完成")
    
    def analyze_cluster_profiles(self):
        """分析聚类客户画像"""
        print("\n" + "="*60)
        print("5. 聚类客户画像分析")
        print("="*60)
        
        # 选择最佳聚类结果进行分析
        if self.clustering_method == 'kmeans' or (
            self.clustering_method == 'both' and 
            self.kmeans_results['best_silhouette'] >= self.dbscan_results['best_silhouette']
        ):
            selected_method = 'kmeans'
            cluster_labels = self.kmeans_results['labels']
            print(f"选择K-Means结果进行客户画像分析")
        else:
            selected_method = 'dbscan'
            cluster_labels = self.dbscan_results['labels']
            print(f"选择DBSCAN结果进行客户画像分析")
        
        # 将聚类标签添加到数据中
        self.df['cluster_label'] = cluster_labels
        
        # 分析每个簇的特征
        cluster_profiles = {}
        unique_clusters = np.unique(cluster_labels)
        
        for cluster_id in unique_clusters:
            if cluster_id == -1:  # 跳过噪声点
                continue
                
            cluster_mask = cluster_labels == cluster_id
            cluster_data = self.df[cluster_mask]
            
            print(f"\n簇 {cluster_id} 分析 ({len(cluster_data):,} 个客户):")
            
            # 计算特征统计
            feature_stats = {}
            for feature in self.selected_features:
                feature_values = cluster_data[feature]
                feature_stats[feature] = {
                    'mean': float(feature_values.mean()),
                    'std': float(feature_values.std()),
                    'median': float(feature_values.median()),
                    'min': float(feature_values.min()),
                    'max': float(feature_values.max())
                }
            
            # 分析目标变量分布
            target_dist = cluster_data['是否购买理财'].value_counts()
            positive_ratio = target_dist.get('1', 0) / len(cluster_data) if '1' in target_dist.index else 0
            
            cluster_profiles[cluster_id] = {
                'size': len(cluster_data),
                'size_ratio': len(cluster_data) / len(self.df),
                'positive_ratio': positive_ratio,
                'feature_stats': feature_stats,
                'target_distribution': target_dist.to_dict()
            }
            
            print(f"  客户数量: {len(cluster_data):,} ({len(cluster_data)/len(self.df)*100:.2f}%)")
            print(f"  正样本比例: {positive_ratio*100:.2f}%")
            
            # 显示前5个重要特征的均值
            print(f"  主要特征均值:")
            for i, feature in enumerate(self.selected_features[:5]):
                mean_val = feature_stats[feature]['mean']
                print(f"    {feature}: {mean_val:.2f}")
        
        self.cluster_profiles = cluster_profiles
        self.selected_clustering_method = selected_method
        
        print(f"✓ 客户画像分析完成")
    
    def identify_high_potential_customers(self):
        """识别高潜力客户"""
        print("\n" + "="*60)
        print("6. 高潜力客户识别")
        print("="*60)
        
        # 基于正样本比例识别高潜力簇
        high_potential_clusters = []
        
        for cluster_id, profile in self.cluster_profiles.items():
            positive_ratio = profile['positive_ratio']
            cluster_size = profile['size']
            
            # 高潜力簇的标准：正样本比例高且簇大小合理
            if positive_ratio > 0.01 and cluster_size > 100:  # 可修改参数
                high_potential_clusters.append({
                    'cluster_id': cluster_id,
                    'positive_ratio': positive_ratio,
                    'size': cluster_size,
                    'potential_score': positive_ratio * np.log(cluster_size)  # 综合评分
                })
        
        # 按潜力评分排序
        high_potential_clusters.sort(key=lambda x: x['potential_score'], reverse=True)
        
        print(f"识别出 {len(high_potential_clusters)} 个高潜力簇:")
        for i, cluster_info in enumerate(high_potential_clusters, 1):
            cluster_id = cluster_info['cluster_id']
            print(f"  {i}. 簇 {cluster_id}: 正样本比例 {cluster_info['positive_ratio']*100:.2f}%, "
                  f"客户数 {cluster_info['size']:,}, 潜力评分 {cluster_info['potential_score']:.2f}")
        
        # 生成高潜力客户名单
        if high_potential_clusters:
            top_cluster_id = high_potential_clusters[0]['cluster_id']
            high_potential_customers = self.df[self.df['cluster_label'] == top_cluster_id].copy()
            
            # 如果有预测概率，结合使用
            if os.path.exists('prediction_scores.csv'):
                prediction_data = pd.read_csv('prediction_scores.csv', encoding='utf-8')
                high_potential_customers = high_potential_customers.merge(
                    prediction_data[['客户号', '购买概率']], 
                    on='客户号', 
                    how='left'
                )
                # 按购买概率排序
                high_potential_customers = high_potential_customers.sort_values('购买概率', ascending=False)
            
            self.high_potential_customers = high_potential_customers
            print(f"\n生成高潜力客户名单: {len(high_potential_customers):,} 个客户")
        else:
            self.high_potential_customers = pd.DataFrame()
            print(f"\n未识别出高潜力客户簇")
        
        self.high_potential_cluster_info = high_potential_clusters
        
        print(f"✓ 高潜力客户识别完成")

    def generate_clustering_visualizations(self):
        """生成聚类可视化图表"""
        print("\n" + "="*60)
        print("7. 生成聚类可视化")
        print("="*60)

        # 1. K-Means肘部法则图
        if self.clustering_method in ['kmeans', 'both'] and self.kmeans_results:
            self.plot_kmeans_elbow_curve()

        # 2. 聚类结果可视化（PCA降维）
        self.plot_cluster_visualization_pca()

        # 3. 聚类结果可视化（t-SNE降维）
        self.plot_cluster_visualization_tsne()

        # 4. 簇特征雷达图
        self.plot_cluster_radar_charts()

        # 5. 簇大小和质量对比
        self.plot_cluster_comparison()

        print(f"✓ 聚类可视化完成")

    def plot_kmeans_elbow_curve(self):
        """绘制K-Means肘部法则图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 肘部法则图
        ax1.plot(self.kmeans_k_range, self.kmeans_results['inertias'], 'bo-')
        ax1.set_xlabel('簇数量 (K)')
        ax1.set_ylabel('簇内平方和 (Inertia)')
        ax1.set_title('K-Means肘部法则图')
        ax1.grid(True, alpha=0.3)

        # 轮廓系数图
        ax2.plot(self.kmeans_k_range, self.kmeans_results['silhouette_scores'], 'ro-')
        ax2.set_xlabel('簇数量 (K)')
        ax2.set_ylabel('轮廓系数')
        ax2.set_title('K-Means轮廓系数图')
        ax2.grid(True, alpha=0.3)

        # 标记最优K值
        best_k = self.kmeans_results['best_k']
        best_silhouette = self.kmeans_results['best_silhouette']
        ax2.axvline(x=best_k, color='red', linestyle='--', alpha=0.7)
        ax2.text(best_k, best_silhouette, f'最优K={best_k}',
                ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_KMeans肘部法则.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✓ K-Means肘部法则图已保存: {filepath}")

    def plot_cluster_visualization_pca(self):
        """使用PCA降维可视化聚类结果"""
        # PCA降维到2D
        pca = PCA(n_components=2, random_state=self.random_state)
        X_pca = pca.fit_transform(self.X_scaled)

        # 创建子图
        if self.clustering_method == 'both':
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
            axes = [ax1, ax2]
            methods = ['kmeans', 'dbscan']
        else:
            fig, ax = plt.subplots(1, 1, figsize=(10, 8))
            axes = [ax]
            methods = [self.clustering_method]

        for i, method in enumerate(methods):
            ax = axes[i] if len(axes) > 1 else axes[0]

            if method == 'kmeans' and self.kmeans_results:
                labels = self.kmeans_results['labels']
                title = f'K-Means聚类结果 (K={self.kmeans_results["best_k"]})'
            elif method == 'dbscan' and self.dbscan_results:
                labels = self.dbscan_results['labels']
                title = f'DBSCAN聚类结果 (eps={self.dbscan_results["best_eps"]:.2f})'
            else:
                continue

            # 绘制散点图
            unique_labels = np.unique(labels)
            colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))

            for label, color in zip(unique_labels, colors):
                if label == -1:
                    # 噪声点用黑色表示
                    mask = labels == label
                    ax.scatter(X_pca[mask, 0], X_pca[mask, 1],
                             c='black', marker='x', s=20, alpha=0.6, label='噪声')
                else:
                    mask = labels == label
                    ax.scatter(X_pca[mask, 0], X_pca[mask, 1],
                             c=[color], s=30, alpha=0.7, label=f'簇 {label}')

            ax.set_xlabel(f'第一主成分 (解释方差: {pca.explained_variance_ratio_[0]:.2%})')
            ax.set_ylabel(f'第二主成分 (解释方差: {pca.explained_variance_ratio_[1]:.2%})')
            ax.set_title(title)
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_聚类结果PCA可视化.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✓ PCA聚类可视化已保存: {filepath}")

    def plot_cluster_visualization_tsne(self):
        """使用t-SNE降维可视化聚类结果"""
        print(f"  正在进行t-SNE降维...")

        # 为了加速，如果数据量太大则采样
        if len(self.X_scaled) > 5000:
            sample_indices = np.random.choice(len(self.X_scaled), 5000, replace=False)
            X_sample = self.X_scaled[sample_indices]

            if self.selected_clustering_method == 'kmeans':
                labels_sample = self.kmeans_results['labels'][sample_indices]
            else:
                labels_sample = self.dbscan_results['labels'][sample_indices]
        else:
            X_sample = self.X_scaled
            if self.selected_clustering_method == 'kmeans':
                labels_sample = self.kmeans_results['labels']
            else:
                labels_sample = self.dbscan_results['labels']

        # t-SNE降维
        tsne = TSNE(n_components=2, random_state=self.random_state, perplexity=30)
        X_tsne = tsne.fit_transform(X_sample)

        # 绘制图表
        plt.figure(figsize=(12, 10))

        unique_labels = np.unique(labels_sample)
        colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))

        for label, color in zip(unique_labels, colors):
            if label == -1:
                # 噪声点用黑色表示
                mask = labels_sample == label
                plt.scatter(X_tsne[mask, 0], X_tsne[mask, 1],
                           c='black', marker='x', s=20, alpha=0.6, label='噪声')
            else:
                mask = labels_sample == label
                plt.scatter(X_tsne[mask, 0], X_tsne[mask, 1],
                           c=[color], s=30, alpha=0.7, label=f'簇 {label}')

        method_name = 'K-Means' if self.selected_clustering_method == 'kmeans' else 'DBSCAN'
        plt.xlabel('t-SNE 第一维')
        plt.ylabel('t-SNE 第二维')
        plt.title(f'{method_name}聚类结果 - t-SNE可视化')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_聚类结果tSNE可视化.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✓ t-SNE聚类可视化已保存: {filepath}")

    def plot_cluster_radar_charts(self):
        """绘制簇特征雷达图"""
        # 选择前8个重要特征进行雷达图分析
        top_features = self.selected_features[:8]

        # 计算每个簇在这些特征上的标准化均值
        cluster_feature_means = {}

        for cluster_id in self.cluster_profiles.keys():
            cluster_means = []
            for feature in top_features:
                mean_val = self.cluster_profiles[cluster_id]['feature_stats'][feature]['mean']
                cluster_means.append(mean_val)
            cluster_feature_means[cluster_id] = cluster_means

        # 对特征值进行归一化（0-1范围）
        all_values = np.array(list(cluster_feature_means.values()))
        min_vals = all_values.min(axis=0)
        max_vals = all_values.max(axis=0)

        for cluster_id in cluster_feature_means:
            normalized_values = (np.array(cluster_feature_means[cluster_id]) - min_vals) / (max_vals - min_vals + 1e-8)
            cluster_feature_means[cluster_id] = normalized_values

        # 绘制雷达图
        n_clusters = len(cluster_feature_means)
        n_cols = 3
        n_rows = (n_clusters + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6*n_rows), subplot_kw=dict(projection='polar'))
        if n_rows == 1:
            axes = axes.reshape(1, -1)

        # 角度设置
        angles = np.linspace(0, 2 * np.pi, len(top_features), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形

        for i, (cluster_id, values) in enumerate(cluster_feature_means.items()):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col]

            # 闭合数据
            values = values.tolist()
            values += values[:1]

            # 绘制雷达图
            ax.plot(angles, values, 'o-', linewidth=2, label=f'簇 {cluster_id}')
            ax.fill(angles, values, alpha=0.25)

            # 设置标签
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(top_features, fontsize=8)
            ax.set_ylim(0, 1)
            ax.set_title(f'簇 {cluster_id} 特征画像\n({self.cluster_profiles[cluster_id]["size"]:,} 个客户)',
                        fontsize=10, pad=20)
            ax.grid(True)

        # 隐藏多余的子图
        for i in range(n_clusters, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            axes[row, col].set_visible(False)

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_簇特征雷达图.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✓ 簇特征雷达图已保存: {filepath}")

    def plot_cluster_comparison(self):
        """绘制簇大小和质量对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        cluster_ids = list(self.cluster_profiles.keys())
        cluster_sizes = [self.cluster_profiles[cid]['size'] for cid in cluster_ids]
        positive_ratios = [self.cluster_profiles[cid]['positive_ratio'] * 100 for cid in cluster_ids]

        # 簇大小柱状图
        bars1 = ax1.bar(range(len(cluster_ids)), cluster_sizes, alpha=0.8, color='skyblue')
        ax1.set_xlabel('簇ID')
        ax1.set_ylabel('客户数量')
        ax1.set_title('各簇客户数量分布')
        ax1.set_xticks(range(len(cluster_ids)))
        ax1.set_xticklabels([f'簇{cid}' for cid in cluster_ids])
        ax1.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, size in zip(bars1, cluster_sizes):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + size*0.01,
                    f'{size:,}', ha='center', va='bottom', fontsize=8)

        # 正样本比例柱状图
        bars2 = ax2.bar(range(len(cluster_ids)), positive_ratios, alpha=0.8, color='lightcoral')
        ax2.set_xlabel('簇ID')
        ax2.set_ylabel('正样本比例 (%)')
        ax2.set_title('各簇正样本比例')
        ax2.set_xticks(range(len(cluster_ids)))
        ax2.set_xticklabels([f'簇{cid}' for cid in cluster_ids])
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, ratio in zip(bars2, positive_ratios):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + ratio*0.01,
                    f'{ratio:.2f}%', ha='center', va='bottom', fontsize=8)

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_簇对比分析.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✓ 簇对比分析图已保存: {filepath}")

    def save_clustering_results(self):
        """保存聚类结果"""
        print("\n" + "="*60)
        print("8. 保存聚类结果")
        print("="*60)

        # 保存聚类后的完整数据
        clustering_data = self.df.copy()

        # 保存主要文件（不带时间戳）
        main_file = 'clustering_results.csv'
        clustering_data.to_csv(main_file, index=False, encoding='utf-8-sig')
        print(f"✓ 聚类结果数据已保存: {main_file}")

        # 保存备份文件（带时间戳）
        backup_file = f'clustering_results_backup_{self.timestamp}.csv'
        clustering_data.to_csv(backup_file, index=False, encoding='utf-8-sig')
        print(f"✓ 备份聚类结果已保存: {backup_file}")

        # 保存高潜力客户名单
        if not self.high_potential_customers.empty:
            potential_file = os.path.join(self.output_dir, f'{self.timestamp}_高潜力客户聚类名单.csv')
            self.high_potential_customers.to_csv(potential_file, index=False, encoding='utf-8-sig')
            print(f"✓ 高潜力客户名单已保存: {potential_file} ({len(self.high_potential_customers):,} 个客户)")

        # 保存聚类分析结果
        analysis_results = {
            'clustering_method': self.selected_clustering_method,
            'cluster_profiles': self.cluster_profiles,
            'high_potential_clusters': self.high_potential_cluster_info,
            'comparison_results': getattr(self, 'comparison_results', {}),
            'parameters': {
                'kmeans_k_range': list(self.kmeans_k_range),
                'dbscan_eps_range': self.dbscan_eps_range.tolist(),
                'dbscan_min_samples_range': list(self.dbscan_min_samples_range),
                'random_state': self.random_state
            }
        }

        if self.kmeans_results:
            analysis_results['kmeans_results'] = {
                'best_k': self.kmeans_results['best_k'],
                'best_silhouette': self.kmeans_results['best_silhouette'],
                'inertias': self.kmeans_results['inertias'],
                'silhouette_scores': self.kmeans_results['silhouette_scores']
            }

        if self.dbscan_results:
            analysis_results['dbscan_results'] = {
                'best_eps': self.dbscan_results['best_eps'],
                'best_min_samples': self.dbscan_results['best_min_samples'],
                'best_silhouette': self.dbscan_results['best_silhouette']
            }

        # 转换所有numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, dict):
                return {str(k): convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj

        analysis_results = convert_numpy_types(analysis_results)

        results_file = os.path.join(self.output_dir, f'{self.timestamp}_聚类分析结果.json')
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, ensure_ascii=False, indent=2, default=str)
        print(f"✓ 聚类分析结果已保存: {results_file}")

    def generate_clustering_report(self):
        """生成聚类分析报告"""
        print("\n" + "="*60)
        print("9. 生成聚类分析报告")
        print("="*60)

        # 创建详细报告
        report_content = f"""# 客户聚类分析报告

## 报告信息
- **生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **聚类方法**: {self.clustering_method.upper()}
- **数据规模**: {len(self.df):,} 个客户

## 1. 聚类算法对比

### 算法配置
- **K-Means**: K值范围 {min(self.kmeans_k_range)}-{max(self.kmeans_k_range)}
- **DBSCAN**: eps范围 {self.dbscan_eps_range[0]:.1f}-{self.dbscan_eps_range[-1]:.1f}, min_samples范围 {min(self.dbscan_min_samples_range)}-{max(self.dbscan_min_samples_range)}

### 算法结果对比
"""

        if self.kmeans_results:
            report_content += f"""
#### K-Means结果
- **最优K值**: {self.kmeans_results['best_k']}
- **轮廓系数**: {self.kmeans_results['best_silhouette']:.4f}
- **簇数量**: {self.kmeans_results['best_k']}
"""

        if self.dbscan_results:
            report_content += f"""
#### DBSCAN结果
- **最优eps**: {self.dbscan_results['best_eps']:.2f}
- **最优min_samples**: {self.dbscan_results['best_min_samples']}
- **轮廓系数**: {self.dbscan_results['best_silhouette']:.4f}
- **簇数量**: {len([cid for cid in self.cluster_profiles.keys() if cid != -1])}
- **噪声比例**: {(np.sum(self.dbscan_results['labels'] == -1) / len(self.dbscan_results['labels']) * 100):.2f}%
"""

        report_content += f"""
### 最终选择
- **选择方法**: {self.selected_clustering_method.upper()}
- **选择原因**: 基于轮廓系数和业务可解释性

## 2. 聚类结果分析

### 簇概览
"""

        for cluster_id, profile in self.cluster_profiles.items():
            report_content += f"""
#### 簇 {cluster_id}
- **客户数量**: {profile['size']:,} ({profile['size_ratio']*100:.2f}%)
- **正样本比例**: {profile['positive_ratio']*100:.2f}%
- **主要特征**:
"""
            # 显示前5个特征的均值
            for i, feature in enumerate(self.selected_features[:5]):
                mean_val = profile['feature_stats'][feature]['mean']
                report_content += f"  - {feature}: {mean_val:.2f}\n"

        report_content += f"""
## 3. 高潜力客户识别

### 高潜力簇排名
"""

        for i, cluster_info in enumerate(self.high_potential_cluster_info, 1):
            cluster_id = cluster_info['cluster_id']
            report_content += f"""
{i}. **簇 {cluster_id}**
   - 正样本比例: {cluster_info['positive_ratio']*100:.2f}%
   - 客户数量: {cluster_info['size']:,}
   - 潜力评分: {cluster_info['potential_score']:.2f}
"""

        if not self.high_potential_customers.empty:
            report_content += f"""
### 高潜力客户名单
- **客户数量**: {len(self.high_potential_customers):,}
- **来源簇**: 簇 {self.high_potential_cluster_info[0]['cluster_id']}
- **预期转化率**: {self.high_potential_cluster_info[0]['positive_ratio']*100:.2f}%
"""

        report_content += f"""
## 4. 业务洞察与建议

### 客户分群特点
"""

        # 分析各簇的业务特点
        for cluster_id, profile in self.cluster_profiles.items():
            positive_ratio = profile['positive_ratio']
            size = profile['size']

            if positive_ratio > 0.05:
                cluster_type = "高价值客户群"
            elif positive_ratio > 0.01:
                cluster_type = "中等价值客户群"
            else:
                cluster_type = "潜在客户群"

            report_content += f"""
#### 簇 {cluster_id} - {cluster_type}
- **客户特征**: 正样本比例 {positive_ratio*100:.2f}%，规模 {size:,} 人
- **营销建议**: """

            if positive_ratio > 0.05:
                report_content += "重点维护，提供高端理财产品\n"
            elif positive_ratio > 0.01:
                report_content += "精准营销，推荐适合的理财产品\n"
            else:
                report_content += "教育培养，提升理财意识\n"

        report_content += f"""
### 营销策略建议

1. **精准营销**
   - 重点关注高潜力簇（簇 {self.high_potential_cluster_info[0]['cluster_id'] if self.high_potential_cluster_info else 'N/A'}）
   - 针对不同簇制定差异化营销策略

2. **产品推荐**
   - 根据簇特征推荐合适的理财产品
   - 考虑客户的风险偏好和资金规模

3. **客户维护**
   - 对高价值客户群提供专属服务
   - 定期跟进中等价值客户的需求变化

4. **潜客培养**
   - 对潜在客户群进行理财教育
   - 通过优惠活动吸引首次购买

## 5. 技术说明

### 聚类算法选择
- **K-Means**: 适用于球形簇，计算效率高
- **DBSCAN**: 能发现任意形状的簇，自动识别噪声点

### 评估指标
- **轮廓系数**: 衡量聚类质量，值越大越好
- **簇内平方和**: K-Means的目标函数，用于肘部法则

### 特征工程
- 使用了 {len(self.selected_features)} 个精选特征
- 进行了标准化处理以消除量纲影响

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        # 保存报告
        report_file = os.path.join(self.output_dir, f'{self.timestamp}_客户聚类分析报告.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✓ 聚类分析报告已保存: {report_file}")

    def run_clustering_analysis(self):
        """运行完整的聚类分析流程"""
        print("开始客户聚类分析...")
        print("="*80)

        # 1. 加载数据
        if not self.load_data():
            return False

        # 2. 准备数据
        self.prepare_clustering_data()

        # 3. 执行聚类
        if self.clustering_method in ['kmeans', 'both']:
            self.perform_kmeans_clustering()

        if self.clustering_method in ['dbscan', 'both']:
            self.perform_dbscan_clustering()

        # 4. 对比结果
        self.compare_clustering_results()

        # 5. 客户画像分析
        self.analyze_cluster_profiles()

        # 6. 识别高潜力客户
        self.identify_high_potential_customers()

        # 7. 生成可视化
        self.generate_clustering_visualizations()

        # 8. 保存结果
        self.save_clustering_results()

        # 9. 生成报告
        self.generate_clustering_report()

        print("\n" + "="*80)
        print("客户聚类分析完成！")
        print(f"聚类方法: {self.selected_clustering_method.upper()}")
        print(f"簇数量: {len(self.cluster_profiles)}")
        print(f"高潜力客户: {len(self.high_potential_customers):,}")
        print(f"所有结果已保存到: {self.output_dir}")
        print("="*80)

        return True

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='客户聚类分析')
    parser.add_argument('--clustering_method', type=str, default='both',
                       choices=['kmeans', 'dbscan', 'both'],
                       help='聚类方法')

    args = parser.parse_args()

    # 创建客户聚类器
    clusterer = CustomerClusterer(clustering_method=args.clustering_method)

    # 运行聚类分析
    success = clusterer.run_clustering_analysis()

    if success:
        print("\n✓ 模块5执行成功")
        return 0
    else:
        print("\n✗ 模块5执行失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
