
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对公理财客户分析看板</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .nav { display: flex; justify-content: center; gap: 20px; margin-bottom: 30px; }
        .nav a { background: white; color: #333; padding: 15px 30px; text-decoration: none; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); transition: transform 0.3s; }
        .nav a:hover { transform: translateY(-2px); }
        .card { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .stat-number { font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }
        .stat-label { font-size: 1.1em; opacity: 0.9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>对公理财客户分析看板</h1>
            <p>基于机器学习的客户预测与聚类分析系统</p>
        </div>
        
        <div class="nav">
            <a href="/processed_eda">数据探索</a>
            <a href="/feature_analysis">特征分析</a>
            <a href="/customer_profile">客户画像</a>
            <a href="/prediction">预测分析</a>
            <a href="/clustering">聚类分析</a>
        </div>
        
        <div class="card">
            <h2>系统概览</h2>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">{{ total_customers }}</div>
                    <div class="stat-label">总客户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ selected_features_count }}</div>
                    <div class="stat-label">选择特征数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ prediction_customers }}</div>
                    <div class="stat-label">预测客户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ cluster_count }}</div>
                    <div class="stat-label">客户群数</div>
                </div>
            </div>
            
            <h3>项目说明</h3>
            <p>本系统采用先进的机器学习技术，对对公理财客户进行深度分析：</p>
            <ul>
                <li><strong>PU学习</strong>：处理未标注数据，生成高质量训练样本</li>
                <li><strong>EasyEnsemble</strong>：解决数据不平衡问题，提高预测准确性</li>
                <li><strong>双算法聚类</strong>：K-Means和DBSCAN结合，全面分析客户群体</li>
                <li><strong>特征工程</strong>：多维度特征选择，提升模型性能</li>
            </ul>
        </div>
    </div>
</body>
</html>
        