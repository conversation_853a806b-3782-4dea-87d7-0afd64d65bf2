#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速数据探索脚本
用于了解宽表.csv的基本结构和特征分布
"""

import pandas as pd
import numpy as np

def quick_data_exploration():
    """快速数据探索"""
    print("=" * 60)
    print("快速数据探索 - 宽表.csv")
    print("=" * 60)
    
    # 读取数据
    try:
        df = pd.read_csv('宽表.csv', encoding='utf-8')
        print(f"✓ 数据加载成功")
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        return
    
    # 基本信息
    print(f"\n1. 数据基本信息:")
    print(f"   - 数据形状: {df.shape}")
    print(f"   - 行数: {df.shape[0]:,}")
    print(f"   - 列数: {df.shape[1]}")
    
    # 列名
    print(f"\n2. 列名列表:")
    for i, col in enumerate(df.columns, 1):
        print(f"   {i:2d}. {col}")
    
    # 目标变量分析
    print(f"\n3. 目标变量分析 - '是否购买理财':")
    target_col = '是否购买理财'
    if target_col in df.columns:
        target_counts = df[target_col].value_counts()
        print(f"   - 取值分布:")
        for value, count in target_counts.items():
            percentage = count / len(df) * 100
            print(f"     {value}: {count:,} ({percentage:.2f}%)")
    else:
        print(f"   ✗ 未找到目标变量列")
    
    # 分类型特征（包含"是否"的列）
    print(f"\n4. 分类型特征（包含'是否'关键词）:")
    categorical_features = [col for col in df.columns if '是否' in col and col != target_col]
    print(f"   - 共找到 {len(categorical_features)} 个分类型特征:")
    for i, col in enumerate(categorical_features, 1):
        print(f"     {i:2d}. {col}")
    
    # 数值型特征基本统计
    print(f"\n5. 数值型特征基本信息:")
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    print(f"   - 数值型列数: {len(numeric_cols)}")
    
    # 缺失值统计
    print(f"\n6. 缺失值统计:")
    missing_stats = df.isnull().sum()
    missing_cols = missing_stats[missing_stats > 0]
    if len(missing_cols) > 0:
        print(f"   - 有缺失值的列数: {len(missing_cols)}")
        print(f"   - 缺失值最多的前10列:")
        for col, count in missing_cols.head(10).items():
            percentage = count / len(df) * 100
            print(f"     {col}: {count:,} ({percentage:.2f}%)")
    else:
        print(f"   - 无缺失值")
    
    # 数据类型统计
    print(f"\n7. 数据类型统计:")
    dtype_counts = df.dtypes.value_counts()
    for dtype, count in dtype_counts.items():
        print(f"   - {dtype}: {count} 列")
    
    print("\n" + "=" * 60)
    print("快速探索完成")
    print("=" * 60)

if __name__ == "__main__":
    quick_data_exploration()
