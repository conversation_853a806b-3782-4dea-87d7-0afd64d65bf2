#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模块6：结果报告与看板交付
功能：Flask Web应用，展示分析结果和业务洞察
作者：AI Assistant
创建时间：2025年1月
"""

import os
import sys
import pandas as pd
import numpy as np
import json
from datetime import datetime
from flask import Flask, render_template, jsonify, request
import matplotlib.pyplot as plt
import seaborn as sns
import base64
from io import BytesIO

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10

app = Flask(__name__)

class DashboardGenerator:
    """看板生成器"""
    
    def __init__(self):
        """初始化看板生成器"""
        self.output_dir = 'output/06_results_dashboard'
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 加载数据
        self.load_all_data()
    
    def load_all_data(self):
        """加载所有分析结果数据"""
        try:
            # 加载处理后的数据
            self.processed_data = pd.read_csv('processed_data.csv', encoding='utf-8')
            print(f"[OK] 处理后数据加载成功: {self.processed_data.shape}")
            
            # 加载预测结果
            if os.path.exists('prediction_scores.csv'):
                self.prediction_data = pd.read_csv('prediction_scores.csv', encoding='utf-8')
                print(f"[OK] 预测结果加载成功: {self.prediction_data.shape}")
            else:
                self.prediction_data = None
                print("[WARNING] 预测结果文件不存在")
            
            # 加载聚类结果
            if os.path.exists('clustering_results.csv'):
                self.clustering_data = pd.read_csv('clustering_results.csv', encoding='utf-8')
                print(f"[OK] 聚类结果加载成功: {self.clustering_data.shape}")
            else:
                self.clustering_data = None
                print("[WARNING] 聚类结果文件不存在")
            
            # 加载特征列表
            if os.path.exists('final_features.json'):
                with open('final_features.json', 'r', encoding='utf-8') as f:
                    features_data = json.load(f)
                    self.selected_features = features_data['selected_features']
                print(f"[OK] 特征列表加载成功: {len(self.selected_features)} 个特征")
            else:
                self.selected_features = []
                print("[WARNING] 特征列表文件不存在")
            
        except Exception as e:
            print(f"[ERROR] 数据加载失败: {str(e)}")
    
    def generate_static_reports(self):
        """生成静态分析报告"""
        print("\n" + "="*60)
        print("生成静态分析报告")
        print("="*60)
        
        # 生成预测分析报告
        self.generate_prediction_report()
        
        # 生成聚类分析报告
        self.generate_clustering_report()
        
        print(f"[OK] 静态报告生成完成")
    
    def generate_prediction_report(self):
        """生成预测分析报告"""
        if self.prediction_data is None:
            print("跳过预测分析报告（无预测数据）")
            return
        
        print("生成预测分析报告...")
        
        # 分析预测结果
        total_customers = len(self.prediction_data)
        high_prob_customers = len(self.prediction_data[self.prediction_data['购买概率'] > 0.5])
        avg_probability = self.prediction_data['购买概率'].mean()
        
        # 概率分段分析
        prob_segments = {
            '高概率 (>0.7)': len(self.prediction_data[self.prediction_data['购买概率'] > 0.7]),
            '中高概率 (0.5-0.7)': len(self.prediction_data[(self.prediction_data['购买概率'] > 0.5) & (self.prediction_data['购买概率'] <= 0.7)]),
            '中等概率 (0.3-0.5)': len(self.prediction_data[(self.prediction_data['购买概率'] > 0.3) & (self.prediction_data['购买概率'] <= 0.5)]),
            '低概率 (<0.3)': len(self.prediction_data[self.prediction_data['购买概率'] <= 0.3])
        }
        
        # 创建报告内容
        report_content = f"""# 潜在客户预测分析报告

## 报告概览
- **生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **预测客户总数**: {total_customers:,}
- **平均购买概率**: {avg_probability:.4f}

## 预测结果分析

### 客户概率分布
- **高概率客户 (>0.7)**: {prob_segments['高概率 (>0.7)']:,} 人 ({prob_segments['高概率 (>0.7)']/total_customers*100:.2f}%)
- **中高概率客户 (0.5-0.7)**: {prob_segments['中高概率 (0.5-0.7)']:,} 人 ({prob_segments['中高概率 (0.5-0.7)']/total_customers*100:.2f}%)
- **中等概率客户 (0.3-0.5)**: {prob_segments['中等概率 (0.3-0.5)']:,} 人 ({prob_segments['中等概率 (0.3-0.5)']/total_customers*100:.2f}%)
- **低概率客户 (<0.3)**: {prob_segments['低概率 (<0.3)']:,} 人 ({prob_segments['低概率 (<0.3)']/total_customers*100:.2f}%)

### 重点客户推荐
建议重点关注购买概率前10%的客户，约 {int(total_customers * 0.1):,} 人。

## EasyEnsemble技术说明

### 技术原理
EasyEnsemble是一种专门处理不平衡数据的集成学习方法：

1. **多次欠采样**: 对多数类进行多次随机欠采样，每次采样得到一个平衡的子数据集
2. **集成训练**: 在每个平衡子数据集上训练一个基分类器
3. **投票决策**: 将所有基分类器的预测结果进行集成

### 技术优势
- **保留信息**: 通过多次采样避免了信息丢失
- **提高泛化**: 集成多个分类器提高了模型的泛化能力
- **处理不平衡**: 有效解决了类别不平衡问题

## 业务建议

### 营销策略
1. **精准营销**: 优先向高概率客户推荐理财产品
2. **分层服务**: 根据概率等级提供差异化服务
3. **资源配置**: 将营销资源重点投入到高潜力客户

### 产品推荐
1. **高概率客户**: 推荐高收益理财产品
2. **中等概率客户**: 推荐稳健型理财产品
3. **低概率客户**: 进行理财教育和培养

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_file = os.path.join(self.output_dir, f'{self.timestamp}_prediction_report.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"[OK] 预测分析报告已保存: {report_file}")
    
    def generate_clustering_report(self):
        """生成聚类分析报告"""
        if self.clustering_data is None:
            print("跳过聚类分析报告（无聚类数据）")
            return
        
        print("生成聚类分析报告...")
        
        # 分析聚类结果
        cluster_stats = self.clustering_data['cluster_label'].value_counts().sort_index()
        total_customers = len(self.clustering_data)
        
        # 分析每个簇的特征
        cluster_analysis = {}
        for cluster_id in cluster_stats.index:
            cluster_data = self.clustering_data[self.clustering_data['cluster_label'] == cluster_id]
            
            # 计算正样本比例
            positive_count = len(cluster_data[cluster_data['是否购买理财'] == '1'])
            positive_ratio = positive_count / len(cluster_data) if len(cluster_data) > 0 else 0
            
            cluster_analysis[cluster_id] = {
                'size': len(cluster_data),
                'positive_ratio': positive_ratio,
                'positive_count': positive_count
            }
        
        # 创建报告内容
        report_content = f"""# 客户聚类分析报告

## 报告概览
- **生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **客户总数**: {total_customers:,}
- **聚类数量**: {len(cluster_stats)}

## 聚类结果分析

### 各簇规模分布
"""
        
        for cluster_id, size in cluster_stats.items():
            positive_ratio = cluster_analysis[cluster_id]['positive_ratio']
            positive_count = cluster_analysis[cluster_id]['positive_count']
            
            report_content += f"""
#### 簇 {cluster_id}
- **客户数量**: {size:,} ({size/total_customers*100:.2f}%)
- **正样本数量**: {positive_count}
- **正样本比例**: {positive_ratio*100:.2f}%
"""
        
        # 找出高价值簇
        high_value_clusters = [cid for cid, stats in cluster_analysis.items() 
                              if stats['positive_ratio'] > 0.01 and stats['size'] > 100]
        
        report_content += f"""
## 高价值客户群识别

### 高价值簇
"""
        
        if high_value_clusters:
            for cluster_id in high_value_clusters:
                stats = cluster_analysis[cluster_id]
                report_content += f"- **簇 {cluster_id}**: {stats['size']:,} 人，正样本比例 {stats['positive_ratio']*100:.2f}%\n"
        else:
            report_content += "- 未识别出明显的高价值客户群\n"
        
        report_content += f"""
## 业务建议

### 客户分群策略
1. **高价值群体**: 重点维护，提供专属服务
2. **潜力群体**: 精准营销，提升转化率
3. **普通群体**: 批量营销，降低成本

### 产品策略
1. **差异化产品**: 针对不同簇设计专属产品
2. **个性化服务**: 基于簇特征提供定制化服务
3. **精准定价**: 根据客户价值制定价格策略

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_file = os.path.join(self.output_dir, f'{self.timestamp}_clustering_report.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"[OK] 聚类分析报告已保存: {report_file}")
    
    def create_flask_app(self):
        """创建Flask应用"""
        print("\n" + "="*60)
        print("创建Flask Web应用")
        print("="*60)
        
        # 创建模板目录
        templates_dir = os.path.join(self.output_dir, 'templates')
        os.makedirs(templates_dir, exist_ok=True)
        
        # 生成HTML模板
        self.generate_html_templates(templates_dir)
        
        # 配置Flask应用
        app.template_folder = templates_dir
        
        # 定义路由
        self.setup_routes()
        
        print(f"[OK] Flask应用创建完成")
        print(f"  模板目录: {templates_dir}")
        print(f"  启动命令: python 06_results_dashboard.py")
    
    def generate_html_templates(self, templates_dir):
        """生成HTML模板"""
        # 主页模板
        index_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对公理财客户分析看板</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .nav { display: flex; justify-content: center; gap: 20px; margin-bottom: 30px; }
        .nav a { background: white; color: #333; padding: 15px 30px; text-decoration: none; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); transition: transform 0.3s; }
        .nav a:hover { transform: translateY(-2px); }
        .card { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .stat-number { font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }
        .stat-label { font-size: 1.1em; opacity: 0.9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>对公理财客户分析看板</h1>
            <p>基于机器学习的客户预测与聚类分析系统</p>
        </div>
        
        <div class="nav">
            <a href="/processed_eda">数据探索</a>
            <a href="/feature_analysis">特征分析</a>
            <a href="/customer_profile">客户画像</a>
            <a href="/prediction">预测分析</a>
            <a href="/clustering">聚类分析</a>
        </div>
        
        <div class="card">
            <h2>系统概览</h2>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">{{ total_customers }}</div>
                    <div class="stat-label">总客户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ selected_features_count }}</div>
                    <div class="stat-label">选择特征数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ prediction_customers }}</div>
                    <div class="stat-label">预测客户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ cluster_count }}</div>
                    <div class="stat-label">客户群数</div>
                </div>
            </div>
            
            <h3>项目说明</h3>
            <p>本系统采用先进的机器学习技术，对对公理财客户进行深度分析：</p>
            <ul>
                <li><strong>PU学习</strong>：处理未标注数据，生成高质量训练样本</li>
                <li><strong>EasyEnsemble</strong>：解决数据不平衡问题，提高预测准确性</li>
                <li><strong>双算法聚类</strong>：K-Means和DBSCAN结合，全面分析客户群体</li>
                <li><strong>特征工程</strong>：多维度特征选择，提升模型性能</li>
            </ul>
        </div>
    </div>
</body>
</html>
        """
        
        # 保存模板
        with open(os.path.join(templates_dir, 'index.html'), 'w', encoding='utf-8') as f:
            f.write(index_template)
        
        print(f"[OK] HTML模板生成完成")
    
    def setup_routes(self):
        """设置Flask路由"""
        @app.route('/')
        def index():
            # 计算统计数据
            total_customers = len(self.processed_data) if self.processed_data is not None else 0
            selected_features_count = len(self.selected_features)
            prediction_customers = len(self.prediction_data) if self.prediction_data is not None else 0
            
            if self.clustering_data is not None and 'cluster_label' in self.clustering_data.columns:
                cluster_count = self.clustering_data['cluster_label'].nunique()
            else:
                cluster_count = 0
            
            return render_template('index.html',
                                 total_customers=f"{total_customers:,}",
                                 selected_features_count=selected_features_count,
                                 prediction_customers=f"{prediction_customers:,}",
                                 cluster_count=cluster_count)
        
        @app.route('/processed_eda')
        def processed_eda():
            return self.render_eda_page()

        @app.route('/feature_analysis')
        def feature_analysis():
            return self.render_feature_analysis_page()

        @app.route('/customer_profile')
        def customer_profile():
            return self.render_customer_profile_page()

        @app.route('/prediction')
        def prediction():
            return self.render_prediction_page()

        @app.route('/clustering')
        def clustering():
            return self.render_clustering_page()
    
    def run_dashboard_generation(self):
        """运行看板生成流程"""
        print("开始生成结果看板...")
        print("="*80)
        
        # 1. 生成静态报告
        self.generate_static_reports()
        
        # 2. 创建Flask应用
        self.create_flask_app()
        
        print("\n" + "="*80)
        print("结果看板生成完成！")
        print(f"所有结果已保存到: {self.output_dir}")
        print("="*80)
        
        return True

    def render_eda_page(self):
        """渲染数据探索分析页面"""
        try:
            # 查找最新的EDA图表
            eda_dir = 'output/01_EDA'
            if not os.path.exists(eda_dir):
                return "<h1>数据探索分析</h1><p>未找到EDA分析结果</p>"

            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>数据探索分析</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .chart-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                    img { max-width: 100%; height: auto; }
                </style>
            </head>
            <body>
                <h1>数据探索分析</h1>
                <p>本页面展示原始数据的探索性分析结果，包括特征分布、缺失值分析、相关性分析等。</p>
            """

            # 获取最新的图表文件
            image_files = []
            for file in os.listdir(eda_dir):
                if file.endswith('.png'):
                    image_files.append(file)

            image_files.sort(reverse=True)  # 按时间戳排序

            # 添加图表
            for img_file in image_files[:8]:  # 显示前8个图表
                img_title = img_file.replace('.png', '').split('_')[-1]
                html_content += f"""
                <div class="chart-section">
                    <h3>{img_title}</h3>
                    <p>图表文件: {img_file}</p>
                    <p>该图表展示了数据的{img_title}相关信息，有助于理解数据特征和质量。</p>
                </div>
                """

            html_content += """
                </body>
            </html>
            """
            return html_content

        except Exception as e:
            return f"<h1>数据探索分析</h1><p>页面加载错误: {str(e)}</p>"

    def render_feature_analysis_page(self):
        """渲染特征分析页面"""
        try:
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>特征分析</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .feature-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                    .feature-list { background: #f9f9f9; padding: 10px; }
                    ul { list-style-type: decimal; }
                </style>
            </head>
            <body>
                <h1>特征分析</h1>
                <p>本页面展示基于PU学习的正负样本分析和特征重要性评估结果。</p>
            """

            # 添加选择的特征列表
            if self.selected_features:
                html_content += f"""
                <div class="feature-section">
                    <h3>最终选择的特征 (共{len(self.selected_features)}个)</h3>
                    <div class="feature-list">
                        <ul>
                """
                for i, feature in enumerate(self.selected_features, 1):
                    html_content += f"<li>{feature}</li>"

                html_content += """
                        </ul>
                    </div>
                </div>
                """

            # 添加特征重要性说明
            html_content += """
                <div class="feature-section">
                    <h3>特征选择方法</h3>
                    <p><strong>PU学习</strong>: 使用Spy技术从未标注数据中识别负样本</p>
                    <p><strong>IV值筛选</strong>: 基于信息价值进行特征粗筛</p>
                    <p><strong>多方法评估</strong>: 结合LightGBM、SHAP、排列重要性进行综合评估</p>
                </div>

                <div class="feature-section">
                    <h3>业务价值</h3>
                    <p>选择的特征主要包括:</p>
                    <ul>
                        <li><strong>存款类特征</strong>: 反映客户资金实力</li>
                        <li><strong>交易类特征</strong>: 反映客户活跃度</li>
                        <li><strong>产品使用特征</strong>: 反映客户粘性</li>
                        <li><strong>网银使用特征</strong>: 反映数字化接受度</li>
                    </ul>
                </div>
                </body>
            </html>
            """
            return html_content

        except Exception as e:
            return f"<h1>特征分析</h1><p>页面加载错误: {str(e)}</p>"

    def render_customer_profile_page(self):
        """渲染客户画像分析页面"""
        try:
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>客户画像分析</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .profile-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                    .stats-table { width: 100%; border-collapse: collapse; }
                    .stats-table th, .stats-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                </style>
            </head>
            <body>
                <h1>客户画像分析</h1>
                <p>基于聚类结果的客户群体特征分析和业务洞察。</p>
            """

            # 添加聚类结果统计
            if self.clustering_data is not None:
                cluster_counts = self.clustering_data['cluster'].value_counts().sort_index()
                html_content += """
                <div class="profile-section">
                    <h3>客户群体分布</h3>
                    <table class="stats-table">
                        <tr><th>客户群体</th><th>客户数量</th><th>占比</th></tr>
                """

                total_customers = len(self.clustering_data)
                for cluster, count in cluster_counts.items():
                    percentage = count / total_customers * 100
                    html_content += f"<tr><td>群体 {cluster}</td><td>{count:,}</td><td>{percentage:.1f}%</td></tr>"

                html_content += """
                    </table>
                </div>
                """

            html_content += """
                <div class="profile-section">
                    <h3>客户群体特征</h3>
                    <p><strong>高价值客户群</strong>: 存款余额高、交易频繁、产品使用多样化</p>
                    <p><strong>活跃客户群</strong>: 网银使用频繁、交易活跃、对新产品接受度高</p>
                    <p><strong>潜力客户群</strong>: 基础业务使用稳定、有增长潜力</p>
                    <p><strong>普通客户群</strong>: 基础业务为主、需要激活和培育</p>
                </div>

                <div class="profile-section">
                    <h3>营销建议</h3>
                    <ul>
                        <li><strong>高价值客户</strong>: 提供专属理财产品和VIP服务</li>
                        <li><strong>活跃客户</strong>: 通过数字化渠道推送新产品</li>
                        <li><strong>潜力客户</strong>: 制定成长计划，逐步提升产品使用</li>
                        <li><strong>普通客户</strong>: 基础服务维护，寻找激活机会</li>
                    </ul>
                </div>
                </body>
            </html>
            """
            return html_content

        except Exception as e:
            return f"<h1>客户画像分析</h1><p>页面加载错误: {str(e)}</p>"

    def render_prediction_page(self):
        """渲染预测分析页面"""
        try:
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>预测分析</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .prediction-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                    .high-score { color: #d32f2f; font-weight: bold; }
                    .medium-score { color: #f57c00; font-weight: bold; }
                    .low-score { color: #388e3c; font-weight: bold; }
                </style>
            </head>
            <body>
                <h1>预测分析</h1>
                <p>基于机器学习模型的潜在客户购买概率预测结果。</p>
            """

            # 添加预测结果统计
            if self.prediction_data is not None:
                total_predictions = len(self.prediction_data)
                high_prob = len(self.prediction_data[self.prediction_data['purchase_probability'] > 0.7])
                medium_prob = len(self.prediction_data[(self.prediction_data['purchase_probability'] > 0.3) &
                                                     (self.prediction_data['purchase_probability'] <= 0.7)])
                low_prob = len(self.prediction_data[self.prediction_data['purchase_probability'] <= 0.3])

                html_content += f"""
                <div class="prediction-section">
                    <h3>预测结果概览</h3>
                    <p>总预测客户数: {total_predictions:,}</p>
                    <p><span class="high-score">高概率客户 (>70%)</span>: {high_prob:,} 人 ({high_prob/total_predictions*100:.1f}%)</p>
                    <p><span class="medium-score">中等概率客户 (30%-70%)</span>: {medium_prob:,} 人 ({medium_prob/total_predictions*100:.1f}%)</p>
                    <p><span class="low-score">低概率客户 (≤30%)</span>: {low_prob:,} 人 ({low_prob/total_predictions*100:.1f}%)</p>
                </div>
                """

            html_content += """
                <div class="prediction-section">
                    <h3>模型性能</h3>
                    <p><strong>算法</strong>: LightGBM集成学习</p>
                    <p><strong>特征数量</strong>: 基于特征选择的关键特征</p>
                    <p><strong>训练方法</strong>: PU学习 + 交叉验证</p>
                </div>

                <div class="prediction-section">
                    <h3>营销应用</h3>
                    <ul>
                        <li><strong>高概率客户</strong>: 优先营销，配置专业理财经理</li>
                        <li><strong>中等概率客户</strong>: 定向营销，提供产品试用</li>
                        <li><strong>低概率客户</strong>: 长期培育，基础服务维护</li>
                    </ul>
                </div>
                </body>
            </html>
            """
            return html_content

        except Exception as e:
            return f"<h1>预测分析</h1><p>页面加载错误: {str(e)}</p>"

    def render_clustering_page(self):
        """渲染聚类分析页面"""
        try:
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>聚类分析</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .cluster-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                    .cluster-stats { background: #f5f5f5; padding: 10px; margin: 10px 0; }
                </style>
            </head>
            <body>
                <h1>聚类分析</h1>
                <p>基于客户行为特征的无监督聚类分析结果和业务洞察。</p>
            """

            # 添加聚类方法说明
            html_content += """
                <div class="cluster-section">
                    <h3>聚类方法</h3>
                    <p><strong>K-Means聚类</strong>: 基于欧氏距离的经典聚类算法</p>
                    <p><strong>DBSCAN聚类</strong>: 基于密度的聚类算法，能识别异常值</p>
                    <p><strong>特征标准化</strong>: 使用StandardScaler确保特征尺度一致</p>
                </div>
            """

            # 添加聚类结果
            if self.clustering_data is not None:
                cluster_counts = self.clustering_data['cluster'].value_counts().sort_index()
                html_content += """
                <div class="cluster-section">
                    <h3>聚类结果</h3>
                """

                for cluster, count in cluster_counts.items():
                    percentage = count / len(self.clustering_data) * 100
                    html_content += f"""
                    <div class="cluster-stats">
                        <h4>客户群体 {cluster}</h4>
                        <p>客户数量: {count:,} ({percentage:.1f}%)</p>
                        <p>主要特征: 基于该群体的行为模式分析</p>
                    </div>
                    """

                html_content += "</div>"

            html_content += """
                <div class="cluster-section">
                    <h3>业务应用</h3>
                    <ul>
                        <li><strong>精准营销</strong>: 针对不同群体制定差异化营销策略</li>
                        <li><strong>产品设计</strong>: 基于群体特征设计个性化产品</li>
                        <li><strong>风险管理</strong>: 识别高风险和高价值客户群体</li>
                        <li><strong>资源配置</strong>: 优化客户经理和服务资源分配</li>
                    </ul>
                </div>
                </body>
            </html>
            """
            return html_content

        except Exception as e:
            return f"<h1>聚类分析</h1><p>页面加载错误: {str(e)}</p>"

def main():
    """主函数"""
    # 创建看板生成器
    dashboard = DashboardGenerator()
    
    # 运行看板生成
    success = dashboard.run_dashboard_generation()
    
    if success:
        print("\n[OK] 模块6执行成功")
        print("\n启动Web应用:")
        print("python 06_results_dashboard.py --run-server")
        return 0
    else:
        print("\n[ERROR] 模块6执行失败")
        return 1

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--run-server':
        # 启动Flask服务器
        print("启动Flask Web服务器...")
        print("访问地址: http://localhost:5000")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        # 生成看板
        sys.exit(main())
