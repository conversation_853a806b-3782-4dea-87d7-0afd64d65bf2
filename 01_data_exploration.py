#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模块1：源数据探索性分析
功能：对宽表.csv进行全面的探索性数据分析
作者：AI Assistant
创建时间：2025年1月
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')
warnings.filterwarnings('ignore')

class DataExplorer:
    """数据探索分析器"""
    
    def __init__(self, data_file: str = '宽表.csv'):
        """
        初始化数据探索器
        
        Args:
            data_file: 数据文件路径，默认为'宽表.csv'
        """
        self.data_file = data_file
        self.df = None
        self.output_dir = 'output/01_EDA'
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 分析结果存储
        self.analysis_results = {
            'basic_info': {},
            'target_analysis': {},
            'numerical_features': {},
            'categorical_features': {},
            'missing_values': {},
            'correlations': {}
        }
    
    def load_data(self) -> bool:
        """
        加载数据文件
        
        Returns:
            bool: 是否成功加载数据
        """
        try:
            print("正在加载数据...")
            self.df = pd.read_csv(self.data_file, encoding='utf-8')
            print(f"✓ 数据加载成功: {self.df.shape[0]:,} 行 × {self.df.shape[1]} 列")
            return True
        except Exception as e:
            print(f"✗ 数据加载失败: {str(e)}")
            return False
    
    def analyze_basic_info(self):
        """分析数据基本信息"""
        print("\n" + "="*60)
        print("1. 数据基本信息分析")
        print("="*60)
        
        # 基本统计信息
        basic_info = {
            'shape': self.df.shape,
            'total_rows': self.df.shape[0],
            'total_columns': self.df.shape[1],
            'memory_usage_mb': self.df.memory_usage(deep=True).sum() / 1024 / 1024,
            'dtypes': self.df.dtypes.value_counts().to_dict()
        }
        
        self.analysis_results['basic_info'] = basic_info
        
        print(f"数据形状: {basic_info['shape']}")
        print(f"总行数: {basic_info['total_rows']:,}")
        print(f"总列数: {basic_info['total_columns']}")
        print(f"内存使用: {basic_info['memory_usage_mb']:.2f} MB")
        print(f"数据类型分布:")
        for dtype, count in basic_info['dtypes'].items():
            print(f"  {dtype}: {count} 列")
    
    def analyze_target_variable(self):
        """分析目标变量"""
        print("\n" + "="*60)
        print("2. 目标变量分析")
        print("="*60)
        
        target_col = '是否购买理财'
        if target_col not in self.df.columns:
            print(f"✗ 未找到目标变量: {target_col}")
            return
        
        # 目标变量分布
        target_counts = self.df[target_col].value_counts()
        target_percentages = self.df[target_col].value_counts(normalize=True) * 100
        
        target_analysis = {
            'column_name': target_col,
            'value_counts': target_counts.to_dict(),
            'percentages': target_percentages.to_dict(),
            'unique_values': self.df[target_col].unique().tolist()
        }
        
        self.analysis_results['target_analysis'] = target_analysis
        
        print(f"目标变量: {target_col}")
        print(f"取值分布:")
        for value, count in target_counts.items():
            percentage = target_percentages[value]
            print(f"  {value}: {count:,} ({percentage:.2f}%)")
        
        # 可视化目标变量分布
        self.plot_target_distribution(target_counts)
    
    def plot_target_distribution(self, target_counts):
        """绘制目标变量分布图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 柱状图
        bars = ax1.bar(range(len(target_counts)), target_counts.values, 
                      color=['#FF6B6B', '#4ECDC4'], alpha=0.8)
        ax1.set_xlabel('目标变量取值')
        ax1.set_ylabel('客户数量')
        ax1.set_title('目标变量分布 - 柱状图')
        ax1.set_xticks(range(len(target_counts)))
        ax1.set_xticklabels(target_counts.index)
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, count) in enumerate(zip(bars, target_counts.values)):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01,
                    f'{count:,}\n({count/target_counts.sum()*100:.1f}%)',
                    ha='center', va='bottom', fontsize=10, fontweight='bold')
        
        # 饼图
        colors = ['#FF6B6B', '#4ECDC4']
        wedges, texts, autotexts = ax2.pie(target_counts.values, labels=target_counts.index,
                                          autopct='%1.2f%%', colors=colors, startangle=90)
        ax2.set_title('目标变量分布 - 饼图')
        
        # 美化饼图文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        plt.tight_layout()
        
        # 保存图片
        filename = f'{self.timestamp}_目标变量分布.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ 目标变量分布图已保存: {filepath}")
    
    def analyze_numerical_features(self):
        """分析数值型特征"""
        print("\n" + "="*60)
        print("3. 数值型特征分析")
        print("="*60)
        
        # 获取数值型列
        numeric_cols = self.df.select_dtypes(include=[np.number]).columns.tolist()
        
        # 排除客户号（如果是数值型）
        if '客户号' in numeric_cols:
            numeric_cols.remove('客户号')
        
        print(f"数值型特征数量: {len(numeric_cols)}")
        
        if len(numeric_cols) == 0:
            print("未找到数值型特征")
            return
        
        # 基本统计信息
        numeric_stats = self.df[numeric_cols].describe()
        
        # 保存统计信息
        stats_file = os.path.join(self.output_dir, f'{self.timestamp}_数值特征统计.csv')
        numeric_stats.to_csv(stats_file, encoding='utf-8-sig')
        print(f"✓ 数值特征统计信息已保存: {stats_file}")
        
        # 存储分析结果
        self.analysis_results['numerical_features'] = {
            'count': len(numeric_cols),
            'columns': numeric_cols,
            'statistics': numeric_stats.to_dict()
        }
        
        # 绘制数值特征分布图（分批处理）
        self.plot_numerical_distributions(numeric_cols)
        
        # 绘制箱线图
        self.plot_numerical_boxplots(numeric_cols)
    
    def plot_numerical_distributions(self, numeric_cols: List[str]):
        """绘制数值特征分布直方图"""
        # 每页最多显示12个特征
        features_per_page = 12
        n_pages = (len(numeric_cols) + features_per_page - 1) // features_per_page
        
        for page in range(n_pages):
            start_idx = page * features_per_page
            end_idx = min((page + 1) * features_per_page, len(numeric_cols))
            page_features = numeric_cols[start_idx:end_idx]
            
            n_features = len(page_features)
            n_cols = 4
            n_rows = (n_features + n_cols - 1) // n_cols
            
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))
            if n_rows == 1:
                axes = axes.reshape(1, -1)
            
            for i, col in enumerate(page_features):
                row = i // n_cols
                col_idx = i % n_cols
                ax = axes[row, col_idx]
                
                # 过滤非空值
                data = self.df[col].dropna()
                
                if len(data) > 0:
                    # 绘制直方图
                    ax.hist(data, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
                    ax.set_title(f'{col}\n(非空值: {len(data):,})', fontsize=10)
                    ax.set_xlabel('数值')
                    ax.set_ylabel('频次')
                    ax.grid(True, alpha=0.3)
                    
                    # 添加统计信息
                    mean_val = data.mean()
                    median_val = data.median()
                    ax.axvline(mean_val, color='red', linestyle='--', alpha=0.8, label=f'均值: {mean_val:.2f}')
                    ax.axvline(median_val, color='green', linestyle='--', alpha=0.8, label=f'中位数: {median_val:.2f}')
                    ax.legend(fontsize=8)
                else:
                    ax.text(0.5, 0.5, '无有效数据', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{col}\n(全部缺失)', fontsize=10)
            
            # 隐藏多余的子图
            for i in range(n_features, n_rows * n_cols):
                row = i // n_cols
                col_idx = i % n_cols
                axes[row, col_idx].set_visible(False)
            
            plt.tight_layout()
            
            # 保存图片
            filename = f'{self.timestamp}_数值特征分布_第{page+1}页.png'
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✓ 数值特征分布图第{page+1}页已保存: {filepath}")
    
    def plot_numerical_boxplots(self, numeric_cols: List[str]):
        """绘制数值特征箱线图"""
        # 每页最多显示8个特征
        features_per_page = 8
        n_pages = (len(numeric_cols) + features_per_page - 1) // features_per_page
        
        for page in range(n_pages):
            start_idx = page * features_per_page
            end_idx = min((page + 1) * features_per_page, len(numeric_cols))
            page_features = numeric_cols[start_idx:end_idx]
            
            n_features = len(page_features)
            n_cols = 4
            n_rows = (n_features + n_cols - 1) // n_cols
            
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))
            if n_rows == 1:
                axes = axes.reshape(1, -1)
            
            for i, col in enumerate(page_features):
                row = i // n_cols
                col_idx = i % n_cols
                ax = axes[row, col_idx]
                
                # 过滤非空值
                data = self.df[col].dropna()
                
                if len(data) > 0:
                    # 绘制箱线图
                    box_plot = ax.boxplot(data, patch_artist=True)
                    box_plot['boxes'][0].set_facecolor('lightblue')
                    box_plot['boxes'][0].set_alpha(0.7)
                    
                    ax.set_title(f'{col}\n(非空值: {len(data):,})', fontsize=10)
                    ax.set_ylabel('数值')
                    ax.grid(True, alpha=0.3)
                else:
                    ax.text(0.5, 0.5, '无有效数据', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{col}\n(全部缺失)', fontsize=10)
            
            # 隐藏多余的子图
            for i in range(n_features, n_rows * n_cols):
                row = i // n_cols
                col_idx = i % n_cols
                axes[row, col_idx].set_visible(False)
            
            plt.tight_layout()
            
            # 保存图片
            filename = f'{self.timestamp}_数值特征箱线图_第{page+1}页.png'
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✓ 数值特征箱线图第{page+1}页已保存: {filepath}")

    def analyze_categorical_features(self):
        """分析分类型特征"""
        print("\n" + "="*60)
        print("4. 分类型特征分析")
        print("="*60)

        # 获取包含"是否"的分类型特征
        categorical_cols = [col for col in self.df.columns if '是否' in col and col != '是否购买理财']

        # 添加其他对象型列
        object_cols = self.df.select_dtypes(include=['object']).columns.tolist()
        for col in object_cols:
            if col not in categorical_cols and col != '客户号':
                categorical_cols.append(col)

        print(f"分类型特征数量: {len(categorical_cols)}")

        if len(categorical_cols) == 0:
            print("未找到分类型特征")
            return

        # 分析每个分类特征
        categorical_analysis = {}
        for col in categorical_cols:
            value_counts = self.df[col].value_counts()
            value_percentages = self.df[col].value_counts(normalize=True) * 100

            categorical_analysis[col] = {
                'unique_count': self.df[col].nunique(),
                'value_counts': value_counts.to_dict(),
                'percentages': value_percentages.to_dict(),
                'missing_count': self.df[col].isnull().sum(),
                'missing_percentage': self.df[col].isnull().sum() / len(self.df) * 100
            }

            print(f"\n{col}:")
            print(f"  唯一值数量: {categorical_analysis[col]['unique_count']}")
            print(f"  缺失值: {categorical_analysis[col]['missing_count']} ({categorical_analysis[col]['missing_percentage']:.2f}%)")
            print(f"  取值分布:")
            for value, count in value_counts.head(10).items():
                percentage = value_percentages[value]
                print(f"    {value}: {count:,} ({percentage:.2f}%)")

        self.analysis_results['categorical_features'] = {
            'count': len(categorical_cols),
            'columns': categorical_cols,
            'analysis': categorical_analysis
        }

        # 绘制分类特征分布图
        self.plot_categorical_distributions(categorical_cols)

    def plot_categorical_distributions(self, categorical_cols: List[str]):
        """绘制分类特征分布图"""
        # 每页最多显示6个特征
        features_per_page = 6
        n_pages = (len(categorical_cols) + features_per_page - 1) // features_per_page

        for page in range(n_pages):
            start_idx = page * features_per_page
            end_idx = min((page + 1) * features_per_page, len(categorical_cols))
            page_features = categorical_cols[start_idx:end_idx]

            n_features = len(page_features)
            n_cols = 3
            n_rows = (n_features + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6*n_rows))
            if n_rows == 1:
                axes = axes.reshape(1, -1)

            for i, col in enumerate(page_features):
                row = i // n_cols
                col_idx = i % n_cols
                ax = axes[row, col_idx]

                # 获取取值分布（只显示前10个）
                value_counts = self.df[col].value_counts().head(10)

                if len(value_counts) > 0:
                    # 绘制柱状图
                    bars = ax.bar(range(len(value_counts)), value_counts.values,
                                 color='lightcoral', alpha=0.8)
                    ax.set_title(f'{col}\n(唯一值: {self.df[col].nunique()})', fontsize=10)
                    ax.set_xlabel('类别')
                    ax.set_ylabel('数量')
                    ax.set_xticks(range(len(value_counts)))
                    ax.set_xticklabels(value_counts.index, rotation=45, ha='right')
                    ax.grid(True, alpha=0.3)

                    # 添加数值标签
                    for bar, count in zip(bars, value_counts.values):
                        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01,
                               f'{count:,}', ha='center', va='bottom', fontsize=8)
                else:
                    ax.text(0.5, 0.5, '无有效数据', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{col}\n(无数据)', fontsize=10)

            # 隐藏多余的子图
            for i in range(n_features, n_rows * n_cols):
                row = i // n_cols
                col_idx = i % n_cols
                axes[row, col_idx].set_visible(False)

            plt.tight_layout()

            # 保存图片
            filename = f'{self.timestamp}_分类特征分布_第{page+1}页.png'
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✓ 分类特征分布图第{page+1}页已保存: {filepath}")

    def analyze_missing_values(self):
        """分析缺失值"""
        print("\n" + "="*60)
        print("5. 缺失值分析")
        print("="*60)

        # 计算缺失值统计
        missing_stats = self.df.isnull().sum()
        missing_percentages = (missing_stats / len(self.df)) * 100

        # 只显示有缺失值的列
        missing_cols = missing_stats[missing_stats > 0].sort_values(ascending=False)

        if len(missing_cols) == 0:
            print("✓ 数据中无缺失值")
            self.analysis_results['missing_values'] = {
                'total_missing_columns': 0,
                'missing_analysis': {}
            }
            return

        print(f"有缺失值的列数: {len(missing_cols)}")
        print(f"缺失值最严重的前20列:")

        missing_analysis = {}
        for col in missing_cols.head(20).index:
            count = missing_stats[col]
            percentage = missing_percentages[col]
            missing_analysis[col] = {
                'missing_count': int(count),
                'missing_percentage': float(percentage)
            }
            print(f"  {col}: {count:,} ({percentage:.2f}%)")

        self.analysis_results['missing_values'] = {
            'total_missing_columns': len(missing_cols),
            'missing_analysis': missing_analysis
        }

        # 绘制缺失值热力图
        self.plot_missing_values_heatmap(missing_cols)

        # 绘制缺失值柱状图
        self.plot_missing_values_bar(missing_cols.head(20))

    def plot_missing_values_heatmap(self, missing_cols):
        """绘制缺失值热力图"""
        if len(missing_cols) == 0:
            return

        # 选择缺失值最多的前30列
        top_missing_cols = missing_cols.head(30).index.tolist()

        # 创建缺失值矩阵
        missing_matrix = self.df[top_missing_cols].isnull()

        # 随机采样行数（如果数据量太大）
        if len(missing_matrix) > 1000:
            sample_indices = np.random.choice(len(missing_matrix), 1000, replace=False)
            missing_matrix = missing_matrix.iloc[sample_indices]

        plt.figure(figsize=(15, 10))
        sns.heatmap(missing_matrix, cbar=True, cmap='viridis',
                   yticklabels=False, xticklabels=True)
        plt.title('缺失值热力图 - 前30个缺失最严重的特征')
        plt.xlabel('特征')
        plt.ylabel('样本')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_缺失值热力图.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✓ 缺失值热力图已保存: {filepath}")

    def plot_missing_values_bar(self, missing_cols):
        """绘制缺失值柱状图"""
        if len(missing_cols) == 0:
            return

        missing_percentages = (missing_cols / len(self.df)) * 100

        plt.figure(figsize=(15, 8))
        bars = plt.bar(range(len(missing_cols)), missing_percentages.values,
                      color='salmon', alpha=0.8)
        plt.title('缺失值比例 - 前20个缺失最严重的特征')
        plt.xlabel('特征')
        plt.ylabel('缺失比例 (%)')
        plt.xticks(range(len(missing_cols)), missing_cols.index, rotation=45, ha='right')
        plt.grid(True, alpha=0.3)

        # 添加数值标签
        for i, (bar, percentage) in enumerate(zip(bars, missing_percentages.values)):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{percentage:.1f}%', ha='center', va='bottom', fontsize=8)

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_缺失值柱状图.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✓ 缺失值柱状图已保存: {filepath}")

    def analyze_correlations(self):
        """分析特征相关性"""
        print("\n" + "="*60)
        print("6. 特征相关性分析")
        print("="*60)

        # 获取数值型特征
        numeric_cols = self.df.select_dtypes(include=[np.number]).columns.tolist()

        # 排除客户号
        if '客户号' in numeric_cols:
            numeric_cols.remove('客户号')

        if len(numeric_cols) < 2:
            print("数值型特征不足，跳过相关性分析")
            return

        # 计算相关性矩阵
        correlation_matrix = self.df[numeric_cols].corr()

        # 保存相关性矩阵
        corr_file = os.path.join(self.output_dir, f'{self.timestamp}_特征相关性矩阵.csv')
        correlation_matrix.to_csv(corr_file, encoding='utf-8-sig')
        print(f"✓ 特征相关性矩阵已保存: {corr_file}")

        # 存储分析结果
        self.analysis_results['correlations'] = {
            'correlation_matrix': correlation_matrix.to_dict()
        }

        # 绘制相关性热力图
        self.plot_correlation_heatmap(correlation_matrix)

        # 分析高相关性特征对
        self.analyze_high_correlations(correlation_matrix)

    def plot_correlation_heatmap(self, correlation_matrix):
        """绘制相关性热力图"""
        # 如果特征太多，只显示前50个
        if len(correlation_matrix) > 50:
            correlation_matrix = correlation_matrix.iloc[:50, :50]

        plt.figure(figsize=(20, 16))

        # 创建掩码，只显示下三角
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))

        # 绘制热力图
        sns.heatmap(correlation_matrix, mask=mask, annot=False, cmap='coolwarm',
                   center=0, square=True, linewidths=0.5, cbar_kws={"shrink": .8})

        plt.title('特征相关性热力图')
        plt.xlabel('特征')
        plt.ylabel('特征')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_特征相关性热力图.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✓ 特征相关性热力图已保存: {filepath}")

    def analyze_high_correlations(self, correlation_matrix):
        """分析高相关性特征对"""
        # 找出高相关性特征对（绝对值 > 0.8）
        high_corr_pairs = []

        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_value = correlation_matrix.iloc[i, j]
                if abs(corr_value) > 0.8:
                    high_corr_pairs.append({
                        'feature1': correlation_matrix.columns[i],
                        'feature2': correlation_matrix.columns[j],
                        'correlation': corr_value
                    })

        if high_corr_pairs:
            print(f"\n发现 {len(high_corr_pairs)} 对高相关性特征 (|相关系数| > 0.8):")
            for pair in sorted(high_corr_pairs, key=lambda x: abs(x['correlation']), reverse=True):
                print(f"  {pair['feature1']} <-> {pair['feature2']}: {pair['correlation']:.3f}")
        else:
            print("\n未发现高相关性特征对 (|相关系数| > 0.8)")

    def generate_summary_report(self):
        """生成分析摘要报告"""
        print("\n" + "="*60)
        print("7. 生成分析摘要报告")
        print("="*60)

        # 创建Markdown报告
        report_content = self.create_markdown_report()

        # 保存报告
        report_file = os.path.join(self.output_dir, f'{self.timestamp}_数据探索分析报告.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✓ 分析摘要报告已保存: {report_file}")

        # 保存分析结果为JSON
        import json

        # 转换分析结果为可序列化的格式
        serializable_results = self.convert_to_serializable(self.analysis_results)

        results_file = os.path.join(self.output_dir, f'{self.timestamp}_分析结果.json')
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2, default=str)

        print(f"✓ 分析结果JSON已保存: {results_file}")

    def convert_to_serializable(self, obj):
        """将对象转换为JSON可序列化的格式"""
        if isinstance(obj, dict):
            return {str(k): self.convert_to_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self.convert_to_serializable(item) for item in obj]
        elif hasattr(obj, 'dtype'):  # pandas/numpy对象
            if hasattr(obj, 'to_dict'):
                return self.convert_to_serializable(obj.to_dict())
            else:
                return str(obj)
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj

    def create_markdown_report(self) -> str:
        """创建Markdown格式的分析报告"""
        report = f"""# 对公理财客户数据探索性分析报告

## 报告信息
- **生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **数据文件**: {self.data_file}
- **分析工具**: Python + Pandas + Matplotlib + Seaborn

## 1. 数据基本信息

### 数据规模
- **总行数**: {self.analysis_results['basic_info']['total_rows']:,}
- **总列数**: {self.analysis_results['basic_info']['total_columns']}
- **内存使用**: {self.analysis_results['basic_info']['memory_usage_mb']:.2f} MB

### 数据类型分布
"""

        for dtype, count in self.analysis_results['basic_info']['dtypes'].items():
            report += f"- **{dtype}**: {count} 列\n"

        report += f"""
## 2. 目标变量分析

### 目标变量: {self.analysis_results['target_analysis']['column_name']}

"""

        for value, count in self.analysis_results['target_analysis']['value_counts'].items():
            percentage = self.analysis_results['target_analysis']['percentages'][value]
            report += f"- **{value}**: {count:,} ({percentage:.2f}%)\n"

        report += f"""
### 关键发现
- 数据存在严重的类别不平衡问题
- 已购买理财客户占比仅 {self.analysis_results['target_analysis']['percentages'].get(1, 0):.2f}%
- 大量客户标签为"未知"，适合使用PU学习方法

## 3. 数值型特征分析

- **数值型特征数量**: {self.analysis_results['numerical_features']['count']}
- **主要特征类型**: 存贷款金额、交易笔数、产品持有数量等

## 4. 分类型特征分析

- **分类型特征数量**: {self.analysis_results['categorical_features']['count']}
- **主要特征类型**: 各类业务使用情况、客户状态标识等

## 5. 数据质量分析

### 缺失值情况
- **有缺失值的列数**: {self.analysis_results['missing_values']['total_missing_columns']}

"""

        if self.analysis_results['missing_values']['total_missing_columns'] > 0:
            report += "### 缺失值最严重的特征\n"
            for col, stats in list(self.analysis_results['missing_values']['missing_analysis'].items())[:10]:
                report += f"- **{col}**: {stats['missing_count']:,} ({stats['missing_percentage']:.2f}%)\n"

        report += f"""
## 6. 关键发现与建议

### 数据特点
1. **样本不平衡**: 正样本比例极低，需要使用专门的不平衡学习技术
2. **高缺失率**: 部分特征缺失率超过90%，需要谨慎处理
3. **特征丰富**: 涵盖存贷款、交易行为、产品使用等多个维度

### 建议
1. **数据预处理**:
   - 删除缺失率过高的特征（>95%）
   - 对重要特征进行缺失值填充
   - 处理异常值和重复值

2. **特征工程**:
   - 对分类特征进行编码
   - 考虑特征组合和衍生特征
   - 进行特征选择和降维

3. **模型策略**:
   - 使用PU学习处理未标注样本
   - 采用集成学习处理不平衡问题
   - 重点关注召回率和F1分数

## 7. 输出文件清单

### 图表文件
- 目标变量分布图
- 数值特征分布图（多页）
- 数值特征箱线图（多页）
- 分类特征分布图（多页）
- 缺失值热力图
- 缺失值柱状图
- 特征相关性热力图

### 数据文件
- 数值特征统计信息CSV
- 特征相关性矩阵CSV
- 分析结果JSON

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return report

    def run_analysis(self):
        """运行完整的数据探索分析"""
        print("开始数据探索性分析...")
        print("="*80)

        # 1. 加载数据
        if not self.load_data():
            return False

        # 2. 基本信息分析
        self.analyze_basic_info()

        # 3. 目标变量分析
        self.analyze_target_variable()

        # 4. 数值型特征分析
        self.analyze_numerical_features()

        # 5. 分类型特征分析
        self.analyze_categorical_features()

        # 6. 缺失值分析
        self.analyze_missing_values()

        # 7. 相关性分析
        self.analyze_correlations()

        # 8. 生成摘要报告
        self.generate_summary_report()

        print("\n" + "="*80)
        print("数据探索性分析完成！")
        print(f"所有结果已保存到: {self.output_dir}")
        print("="*80)

        return True

def main():
    """主函数"""
    # 创建数据探索器
    explorer = DataExplorer()

    # 运行分析
    success = explorer.run_analysis()

    if success:
        print("\n✓ 模块1执行成功")
        return 0
    else:
        print("\n✗ 模块1执行失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
