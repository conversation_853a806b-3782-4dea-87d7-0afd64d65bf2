#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的Web看板测试
"""

from flask import Flask, render_template_string
import os
import json
import pandas as pd

app = Flask(__name__)

# 全局数据变量
dashboard_data = {
    'total_customers': 0,
    'selected_features_count': 0,
    'prediction_customers': 0,
    'cluster_count': 0,
    'selected_features': []
}

def load_dashboard_data():
    """加载看板数据"""
    global dashboard_data
    
    try:
        # 加载处理后的数据
        if os.path.exists('processed_data.csv'):
            processed_data = pd.read_csv('processed_data.csv', encoding='utf-8')
            dashboard_data['total_customers'] = len(processed_data)
            print(f"加载处理后数据: {len(processed_data):,} 行")
        
        # 加载预测结果
        if os.path.exists('prediction_scores.csv'):
            prediction_data = pd.read_csv('prediction_scores.csv', encoding='utf-8')
            dashboard_data['prediction_customers'] = len(prediction_data)
            print(f"加载预测结果: {len(prediction_data):,} 行")
        
        # 加载聚类结果
        if os.path.exists('clustering_results.csv'):
            clustering_data = pd.read_csv('clustering_results.csv', encoding='utf-8')
            if 'cluster_label' in clustering_data.columns:
                dashboard_data['cluster_count'] = clustering_data['cluster_label'].nunique()
            print(f"加载聚类结果: {len(clustering_data):,} 行")
        
        # 加载特征列表
        if os.path.exists('final_features.json'):
            with open('final_features.json', 'r', encoding='utf-8') as f:
                features_data = json.load(f)
                dashboard_data['selected_features'] = features_data['selected_features']
                dashboard_data['selected_features_count'] = len(features_data['selected_features'])
            print(f"加载特征列表: {len(features_data['selected_features'])} 个特征")
        
        print("数据加载完成")
        
    except Exception as e:
        print(f"数据加载错误: {str(e)}")

@app.route('/')
def index():
    """首页"""
    template = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>对公理财客户分析看板</title>
        <style>
            body { font-family: 'Microsoft YaHei', sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
            .nav { display: flex; justify-content: center; gap: 20px; margin-bottom: 30px; }
            .nav a { background: white; color: #333; padding: 15px 30px; text-decoration: none; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); transition: transform 0.3s; }
            .nav a:hover { transform: translateY(-2px); }
            .card { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin-bottom: 20px; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
            .stat-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
            .stat-number { font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }
            .stat-label { font-size: 1.1em; opacity: 0.9; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>对公理财客户分析看板</h1>
                <p>基于机器学习的客户预测与聚类分析系统</p>
            </div>
            
            <div class="nav">
                <a href="/eda">数据探索</a>
                <a href="/features">特征分析</a>
                <a href="/profile">客户画像</a>
                <a href="/prediction">预测分析</a>
                <a href="/clustering">聚类分析</a>
            </div>
            
            <div class="card">
                <h2>系统概览</h2>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">{{ total_customers }}</div>
                        <div class="stat-label">总客户数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ selected_features_count }}</div>
                        <div class="stat-label">选择特征数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ prediction_customers }}</div>
                        <div class="stat-label">预测客户数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ cluster_count }}</div>
                        <div class="stat-label">客户群数</div>
                    </div>
                </div>
                
                <h3>项目说明</h3>
                <p>本系统采用先进的机器学习技术，对对公理财客户进行深度分析：</p>
                <ul>
                    <li><strong>PU学习</strong>：处理未标注数据，生成高质量训练样本</li>
                    <li><strong>EasyEnsemble</strong>：解决数据不平衡问题，提高预测准确性</li>
                    <li><strong>双算法聚类</strong>：K-Means和DBSCAN结合，全面分析客户群体</li>
                    <li><strong>特征工程</strong>：多维度特征选择，提升模型性能</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    """
    
    return render_template_string(template,
                                total_customers=f"{dashboard_data['total_customers']:,}",
                                selected_features_count=dashboard_data['selected_features_count'],
                                prediction_customers=f"{dashboard_data['prediction_customers']:,}",
                                cluster_count=dashboard_data['cluster_count'])

@app.route('/features')
def features():
    """特征分析页面"""
    template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>特征分析</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .feature-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
            .feature-list { background: #f9f9f9; padding: 10px; }
            ul { list-style-type: decimal; }
            .nav { margin-bottom: 20px; }
            .nav a { margin-right: 10px; padding: 5px 10px; background: #007bff; color: white; text-decoration: none; }
        </style>
    </head>
    <body>
        <div class="nav">
            <a href="/">首页</a>
            <a href="/eda">数据探索</a>
            <a href="/features">特征分析</a>
            <a href="/profile">客户画像</a>
            <a href="/prediction">预测分析</a>
            <a href="/clustering">聚类分析</a>
        </div>
        
        <h1>特征分析</h1>
        <p>本页面展示基于PU学习的正负样本分析和特征重要性评估结果。</p>
        
        <div class="feature-section">
            <h3>最终选择的特征 (共{{ feature_count }}个)</h3>
            <div class="feature-list">
                <ul>
                {% for feature in features %}
                    <li>{{ feature }}</li>
                {% endfor %}
                </ul>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>特征选择方法</h3>
            <p><strong>PU学习</strong>: 使用Spy技术从未标注数据中识别负样本</p>
            <p><strong>IV值筛选</strong>: 基于信息价值进行特征粗筛</p>
            <p><strong>多方法评估</strong>: 结合LightGBM、SHAP、排列重要性进行综合评估</p>
        </div>
    </body>
    </html>
    """
    
    return render_template_string(template,
                                feature_count=len(dashboard_data['selected_features']),
                                features=dashboard_data['selected_features'])

@app.route('/eda')
def eda():
    """数据探索页面"""
    return "<h1>数据探索分析</h1><p>展示原始数据的探索性分析结果</p><a href='/'>返回首页</a>"

@app.route('/profile')
def profile():
    """客户画像页面"""
    return "<h1>客户画像分析</h1><p>基于聚类结果的客户群体特征分析</p><a href='/'>返回首页</a>"

@app.route('/prediction')
def prediction():
    """预测分析页面"""
    return "<h1>预测分析</h1><p>潜在客户购买概率预测结果</p><a href='/'>返回首页</a>"

@app.route('/clustering')
def clustering():
    """聚类分析页面"""
    return "<h1>聚类分析</h1><p>客户聚类分析结果和业务洞察</p><a href='/'>返回首页</a>"

if __name__ == "__main__":
    print("启动简化Web看板...")
    load_dashboard_data()
    print("访问地址: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
