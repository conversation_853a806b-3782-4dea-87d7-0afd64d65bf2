# 数据预处理与特征工程报告

## 报告信息
- **生成时间**: 2025年06月08日 15:43:36
- **数据文件**: 宽表.csv
- **处理工具**: Python + Pandas + Scikit-learn

## 1. 数据处理概览

### 数据规模变化
- **原始数据**: 334,001 行 x 49 列
- **处理后数据**: 320,468 行 x 36 列
- **行数变化**: -13,533 (-4.05%)
- **列数变化**: -13 (-26.53%)

## 2. 数据清洗

### 缺失值处理
- **删除的高缺失率列数**: 0
- **填充缺失值的列数**: 13

### 缺失值填充详情
- **首个账户开户日**: mode 填充，处理 1 个缺失值 (0.00%)
- **当年月均交易笔数**: zero_fill 填充，处理 121,279 个缺失值 (36.31%)
- **表内贷款余额年日均**: zero_fill 填充，处理 307,453 个缺失值 (92.05%)
- **融资年日均**: zero_fill 填充，处理 307,453 个缺失值 (92.05%)
- **存款总额年日均**: zero_fill 填充，处理 5,452 个缺失值 (1.63%)
- **贷款本年发生额**: zero_fill 填充，处理 314,011 个缺失值 (94.01%)
- **贷款余额**: zero_fill 填充，处理 314,011 个缺失值 (94.01%)
- **近12月活期转出金额**: zero_fill 填充，处理 141,700 个缺失值 (42.43%)
- **近12月活期转出笔数**: zero_fill 填充，处理 141,700 个缺失值 (42.43%)
- **近12月活期转入金额**: zero_fill 填充，处理 141,064 个缺失值 (42.23%)
- **近12月活期转入笔数**: zero_fill 填充，处理 141,064 个缺失值 (42.23%)
- **近12月代发工资金额**: zero_fill 填充，处理 307,847 个缺失值 (92.17%)
- **近12月代发工资笔数**: zero_fill 填充，处理 299,912 个缺失值 (89.79%)

### 重复值处理
- **重复行数**: 0
- **处理方式**: none

## 3. 特征工程

### 分类特征编码
- **现金管理-银企直连-是否本年使用**: label_encoding (2 个唯一值)
- **代扣公积金-是否本年使用**: label_encoding (2 个唯一值)
- **代扣税费-是否本年签约**: label_encoding (2 个唯一值)
- **代扣税费-是否本年使用**: label_encoding (2 个唯一值)
- **自助缴费-是否本年使用**: label_encoding (2 个唯一值)
- **收银宝-是否本年使用**: label_encoding (2 个唯一值)
- **代发工资-是否本年签约**: label_encoding (2 个唯一值)
- **代发工资-是否本年使用**: label_encoding (2 个唯一值)
- **是否销户**: one_hot_encoding (3 个唯一值)
- **是否有贷户**: label_encoding (2 个唯一值)
- **是否当年新开户**: label_encoding (2 个唯一值)
- **是否达标有效户**: label_encoding (2 个唯一值)
- **是否达标价值户**: label_encoding (2 个唯一值)
- **是否活跃客户**: label_encoding (2 个唯一值)
- **是否代发客户**: label_encoding (2 个唯一值)
- **是否同业**: label_encoding (2 个唯一值)

### 日期特征处理
- **原始列**: 首个账户开户日
- **新生成列**: 开户距今天数, 开户年份
- **参考日期**: 2024-12-31 00:00:00

### 衍生特征创建

## 4. 数据质量优化

### 异常值处理
- **存贷款EVA**: removal，处理 377 个异常值 (0.11%)
- **当年月均交易笔数**: quantile_clipping_conservative，处理 1662 个异常值 (0.50%)
- **表内贷款余额年日均**: removal，处理 775 个异常值 (0.23%)
- **融资年日均**: removal，处理 3029 个异常值 (0.91%)
- **存款总额年日均**: removal，处理 769 个异常值 (0.23%)
- **持有产品总数**: removal，处理 1497 个异常值 (0.45%)
- **企业网银-本年登录次数**: quantile_clipping_conservative，处理 1638 个异常值 (0.50%)
- **一般贷款余额**: removal，处理 1662 个异常值 (0.51%)
- **一般贷款本年发生额**: removal，处理 2100 个异常值 (0.64%)
- **贷款本年发生额**: removal，处理 607 个异常值 (0.19%)
- **贷款余额**: removal，处理 1505 个异常值 (0.47%)
- **近12月活期转出金额**: quantile_clipping_conservative，处理 1609 个异常值 (0.50%)
- **近12月活期转出笔数**: quantile_clipping_conservative，处理 1606 个异常值 (0.50%)
- **近12月活期转入金额**: quantile_clipping_conservative，处理 1609 个异常值 (0.50%)
- **近12月活期转入笔数**: quantile_clipping_conservative，处理 1606 个异常值 (0.50%)
- **近12月活期交易总金额**: quantile_clipping_conservative，处理 1609 个异常值 (0.50%)
- **近12月活期交易总笔数**: quantile_clipping_conservative，处理 1609 个异常值 (0.50%)
- **近12月代发工资金额**: quantile_clipping_conservative，处理 1609 个异常值 (0.50%)
- **近12月代发工资笔数**: quantile_clipping_conservative，处理 1574 个异常值 (0.49%)
- **开户距今天数**: removal，处理 740 个异常值 (0.23%)
- **开户年份**: removal，处理 472 个异常值 (0.15%)

### 高相关性特征处理
- **删除特征数**: 4
- **相关性阈值**: 0.99
- **删除的特征**: 一般贷款余额, 开户年份, 近12月活期转入金额, 近12月活期转出金额

## 5. 处理结果总结

### 删除的列
- **总删除列数**: 15
- **删除列列表**:
  - 大额存单年日均余额
  - 结构性存款年日均余额
  - 国际结算-本年交易量折美元
  - 即期结售汇-本年交易量折美元
  - 线上供应链-余额
  - 线上供应链-本年发生额
  - 国内保函-余额
  - 银承贴现-余额
  - 银承贴现-本年发生额
  - 近12月代扣公积金金额
  - 近12月代扣公积金笔数
  - 一般贷款余额
  - 开户年份
  - 近12月活期转入金额
  - 近12月活期转出金额

### 数据质量改善
1. **完整性**: 消除了高缺失率特征，填充了重要特征的缺失值
2. **一致性**: 统一了分类特征编码，处理了重复数据
3. **准确性**: 识别并处理了异常值
4. **相关性**: 删除了高度相关的冗余特征

### 特征工程效果
1. **特征数量**: 从 49 个特征优化为 36 个特征
2. **特征质量**: 增加了衍生特征，提升了特征的业务解释性
3. **数据可用性**: 所有特征均为数值型，可直接用于机器学习

## 6. 后续建议

1. **特征选择**: 建议使用统计方法和机器学习方法进一步筛选重要特征
2. **特征缩放**: 在模型训练前考虑对特征进行标准化或归一化
3. **特征验证**: 验证衍生特征的业务合理性和预测能力
4. **持续监控**: 定期检查数据质量，更新处理策略

---
*报告生成时间: 2025-06-08 15:43:36*
