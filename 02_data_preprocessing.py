#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模块2：数据预处理与特征工程
功能：数据清洗、特征编码、异常值处理、特征工程
作者：AI Assistant
创建时间：2025年1月
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.impute import SimpleImputer

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10
plt.style.use('default')
warnings.filterwarnings('ignore')

class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self, data_file: str = '宽表.csv'):
        """
        初始化数据预处理器
        
        Args:
            data_file: 数据文件路径，默认为'宽表.csv'
        """
        self.data_file = data_file
        self.df_original = None  # 原始数据
        self.df_processed = None  # 处理后数据
        self.output_dir = 'output/02_processing'
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 处理统计信息
        self.processing_stats = {
            'original_shape': None,
            'processed_shape': None,
            'dropped_columns': [],
            'missing_value_handling': {},
            'feature_encoding': {},
            'outlier_handling': {},
            'feature_engineering': {}
        }
        
        # 可修改参数 - 数据清洗阈值
        self.missing_threshold = 0.95  # 缺失率超过95%的列将被删除
        self.outlier_threshold = 3.0   # Z-score超过3的值视为异常值
        self.correlation_threshold = 0.99  # 相关系数超过0.99的特征对将删除其中一个
    
    def load_data(self) -> bool:
        """
        加载数据文件
        
        Returns:
            bool: 是否成功加载数据
        """
        try:
            print("正在加载数据...")
            self.df_original = pd.read_csv(self.data_file, encoding='utf-8')
            self.processing_stats['original_shape'] = self.df_original.shape
            print(f"[OK] 数据加载成功: {self.df_original.shape[0]:,} 行 x {self.df_original.shape[1]} 列")
            
            # 复制数据用于处理
            self.df_processed = self.df_original.copy()
            return True
        except Exception as e:
            print(f"[ERROR] 数据加载失败: {str(e)}")
            return False
    
    def handle_missing_values(self):
        """处理缺失值"""
        print("\n" + "="*60)
        print("1. 缺失值处理")
        print("="*60)
        
        # 计算缺失率
        missing_rates = self.df_processed.isnull().sum() / len(self.df_processed)
        
        # 识别高缺失率的列
        high_missing_cols = missing_rates[missing_rates > self.missing_threshold].index.tolist()
        
        print(f"缺失率超过 {self.missing_threshold*100}% 的列数: {len(high_missing_cols)}")
        
        # 删除高缺失率的列
        if high_missing_cols:
            print(f"删除高缺失率列:")
            for col in high_missing_cols:
                print(f"  - {col}: {missing_rates[col]*100:.2f}%")
            
            self.df_processed = self.df_processed.drop(columns=high_missing_cols)
            self.processing_stats['dropped_columns'].extend(high_missing_cols)
        
        # 处理剩余的缺失值
        remaining_missing = self.df_processed.isnull().sum()
        remaining_missing_cols = remaining_missing[remaining_missing > 0].index.tolist()
        
        if remaining_missing_cols:
            print(f"\n处理剩余缺失值的列数: {len(remaining_missing_cols)}")
            
            for col in remaining_missing_cols:
                missing_count = remaining_missing[col]
                missing_rate = missing_count / len(self.df_processed)
                
                if self.df_processed[col].dtype in ['object']:
                    # 分类型特征：用众数填充
                    mode_value = self.df_processed[col].mode()
                    if len(mode_value) > 0:
                        fill_value = mode_value[0]
                        self.df_processed[col].fillna(fill_value, inplace=True)
                        self.processing_stats['missing_value_handling'][col] = {
                            'method': 'mode',
                            'fill_value': fill_value,
                            'missing_count': int(missing_count),
                            'missing_rate': float(missing_rate)
                        }
                        print(f"  - {col}: 用众数 '{fill_value}' 填充 {missing_count} 个缺失值")
                else:
                    # 数值型特征：统一用0填充（修改：不再使用中位数）
                    fill_value = 0
                    self.df_processed[col].fillna(fill_value, inplace=True)
                    self.processing_stats['missing_value_handling'][col] = {
                        'method': 'zero_fill',
                        'fill_value': float(fill_value),
                        'missing_count': int(missing_count),
                        'missing_rate': float(missing_rate)
                    }
                    print(f"  - {col}: 用0填充 {missing_count} 个缺失值")
        
        print(f"\n[OK] 缺失值处理完成")
        print(f"  删除列数: {len(high_missing_cols)}")
        print(f"  填充列数: {len(remaining_missing_cols)}")
    
    def handle_duplicate_values(self):
        """处理重复值"""
        print("\n" + "="*60)
        print("2. 重复值处理")
        print("="*60)
        
        # 检查重复行
        duplicate_count = self.df_processed.duplicated().sum()
        
        if duplicate_count > 0:
            print(f"发现 {duplicate_count} 行重复数据")
            self.df_processed = self.df_processed.drop_duplicates()
            print(f"[OK] 已删除重复数据")
        else:
            print("[OK] 未发现重复数据")
        
        self.processing_stats['duplicate_handling'] = {
            'duplicate_count': int(duplicate_count),
            'action': 'removed' if duplicate_count > 0 else 'none'
        }
    
    def encode_categorical_features(self):
        """编码分类型特征"""
        print("\n" + "="*60)
        print("3. 分类特征编码")
        print("="*60)
        
        # 识别分类型特征
        categorical_cols = []
        
        # 包含"是否"的特征
        boolean_cols = [col for col in self.df_processed.columns if '是否' in col and col != '是否购买理财']
        categorical_cols.extend(boolean_cols)
        
        # 其他对象型列（排除客户号、日期和目标变量）
        object_cols = self.df_processed.select_dtypes(include=['object']).columns.tolist()
        for col in object_cols:
            if col not in ['客户号', '首个账户开户日', '是否购买理财'] and col not in categorical_cols:
                categorical_cols.append(col)
        
        print(f"需要编码的分类特征数量: {len(categorical_cols)}")
        
        for col in categorical_cols:
            unique_count = self.df_processed[col].nunique()
            print(f"  - {col}: {unique_count} 个唯一值")
            
            if unique_count == 2:
                # 二分类特征：使用Label Encoding
                le = LabelEncoder()
                self.df_processed[col] = le.fit_transform(self.df_processed[col].astype(str))
                
                self.processing_stats['feature_encoding'][col] = {
                    'method': 'label_encoding',
                    'unique_count': int(unique_count),
                    'classes': le.classes_.tolist()
                }
                print(f"    → Label Encoding: {le.classes_}")
                
            elif unique_count <= 10:
                # 多分类特征（类别数<=10）：使用One-Hot Encoding
                dummies = pd.get_dummies(self.df_processed[col], prefix=col, drop_first=True)
                self.df_processed = pd.concat([self.df_processed, dummies], axis=1)
                self.df_processed = self.df_processed.drop(columns=[col])
                
                self.processing_stats['feature_encoding'][col] = {
                    'method': 'one_hot_encoding',
                    'unique_count': int(unique_count),
                    'new_columns': dummies.columns.tolist()
                }
                print(f"    → One-Hot Encoding: 生成 {len(dummies.columns)} 个新特征")
                
            else:
                # 高基数分类特征：使用Target Encoding（如果有目标变量）
                if '是否购买理财' in self.df_processed.columns:
                    # 计算每个类别的目标变量均值
                    target_col = '是否购买理财'
                    # 将"未知"转换为NaN，只使用已知标签计算均值
                    temp_target = self.df_processed[target_col].replace('未知', np.nan)
                    temp_target = pd.to_numeric(temp_target, errors='coerce')
                    
                    category_means = temp_target.groupby(self.df_processed[col]).mean()
                    global_mean = temp_target.mean()
                    
                    # 用均值替换类别，缺失的类别用全局均值
                    self.df_processed[col] = self.df_processed[col].map(category_means).fillna(global_mean)
                    
                    self.processing_stats['feature_encoding'][col] = {
                        'method': 'target_encoding',
                        'unique_count': int(unique_count),
                        'global_mean': float(global_mean)
                    }
                    print(f"    → Target Encoding: 全局均值 {global_mean:.4f}")
                else:
                    # 没有目标变量时，删除高基数分类特征
                    self.df_processed = self.df_processed.drop(columns=[col])
                    self.processing_stats['dropped_columns'].append(col)
                    print(f"    → 删除高基数特征")
        
        print(f"[OK] 分类特征编码完成")
    
    def handle_date_features(self):
        """处理日期特征"""
        print("\n" + "="*60)
        print("4. 日期特征处理")
        print("="*60)
        
        date_col = '首个账户开户日'
        
        if date_col in self.df_processed.columns:
            print(f"处理日期特征: {date_col}")
            
            # 转换为日期类型
            self.df_processed[date_col] = pd.to_datetime(self.df_processed[date_col], errors='coerce')
            
            # 计算距今天数
            reference_date = pd.Timestamp('2024-12-31')  # 可修改参数：参考日期
            self.df_processed['开户距今天数'] = (reference_date - self.df_processed[date_col]).dt.days
            
            # 提取年份
            self.df_processed['开户年份'] = self.df_processed[date_col].dt.year
            
            # 删除原始日期列
            self.df_processed = self.df_processed.drop(columns=[date_col])
            
            self.processing_stats['feature_engineering']['date_features'] = {
                'original_column': date_col,
                'new_columns': ['开户距今天数', '开户年份'],
                'reference_date': str(reference_date)
            }
            
            print(f"  [OK] 生成新特征: 开户距今天数, 开户年份")
            print(f"  [OK] 删除原始日期列: {date_col}")
        else:
            print(f"未找到日期特征: {date_col}")
        
        print(f"[OK] 日期特征处理完成")

    def handle_outliers(self):
        """处理异常值（全数据保留版）"""
        print("\n" + "="*60)
        print("5. 异常值处理（全数据保留版）")
        print("="*60)

        # 获取数值型特征
        numeric_cols = self.df_processed.select_dtypes(include=[np.number]).columns.tolist()

        # 排除客户号和目标变量
        exclude_cols = ['客户号']
        if '是否购买理财' in numeric_cols:
            exclude_cols.append('是否购买理财')

        # 识别布尔型特征（包含"是否"的特征，这些不应该有异常值）
        boolean_features = [col for col in numeric_cols if '是否' in col]

        # 识别交易类特征（需要特殊处理）
        transaction_features = [col for col in numeric_cols
                              if any(keyword in col for keyword in ['交易', '笔数', '金额', '登录次数'])]

        numeric_cols = [col for col in numeric_cols if col not in exclude_cols]

        print(f"检查异常值的数值特征数量: {len(numeric_cols)}")
        print(f"  其中布尔型特征: {len([col for col in boolean_features if col in numeric_cols])}")
        print(f"  其中交易类特征: {len([col for col in transaction_features if col in numeric_cols])}")
        print(f"策略: 只进行数值截断，不删除任何数据行")

        outlier_stats = {}
        original_row_count = len(self.df_processed)

        for col in numeric_cols:
            # 跳过布尔型特征的异常值检测
            if col in boolean_features:
                print(f"  - {col}: 跳过异常值检测（布尔型特征）")
                continue

            # 检查列是否有足够的变异性
            if self.df_processed[col].std() == 0:
                print(f"  - {col}: 跳过异常值检测（无变异性）")
                continue

            # 统一使用截断策略，不删除任何数据行
            # 对于交易类特征，使用更宽松的分位数
            if col in transaction_features:
                # 使用更宽松的分位数方法
                q1 = self.df_processed[col].quantile(0.001)  # 0.1%分位数
                q99 = self.df_processed[col].quantile(0.999)  # 99.9%分位数
            else:
                # 对其他数值特征使用标准分位数
                q1 = self.df_processed[col].quantile(0.005)  # 0.5%分位数
                q99 = self.df_processed[col].quantile(0.995)  # 99.5%分位数

            # 检测异常值
            outliers = (self.df_processed[col] < q1) | (self.df_processed[col] > q99)
            outlier_count = outliers.sum()

            if outlier_count > 0:
                # 只进行截断，不删除数据
                original_values = self.df_processed[col].copy()
                self.df_processed[col] = np.clip(self.df_processed[col], q1, q99)

                outlier_stats[col] = {
                    'method': 'quantile_clipping_only',
                    'outlier_count': int(outlier_count),
                    'outlier_rate': float(outlier_count / len(self.df_processed)),
                    'q1': float(q1),
                    'q99': float(q99),
                    'feature_type': 'transaction' if col in transaction_features else 'numeric'
                }

                feature_type_label = "[交易类特征]" if col in transaction_features else "[数值特征]"
                print(f"  - {col}: 截断 {outlier_count} 个异常值 ({outlier_count/len(self.df_processed)*100:.2f}%) {feature_type_label}")
            else:
                print(f"  - {col}: 无异常值需要处理")

        self.processing_stats['outlier_handling'] = outlier_stats

        # 验证数据行数保持不变
        final_row_count = len(self.df_processed)
        if final_row_count == original_row_count:
            print(f"[OK] 数据行数保持不变: {final_row_count:,} 行")
        else:
            print(f"[WARNING] 数据行数发生变化: {original_row_count:,} -> {final_row_count:,}")

        if outlier_stats:
            print(f"[OK] 异常值处理完成，处理了 {len(outlier_stats)} 个特征（仅截断，未删除数据）")
        else:
            print(f"[OK] 未发现需要处理的异常值")

    def remove_highly_correlated_features(self):
        """删除高相关性特征"""
        print("\n" + "="*60)
        print("6. 高相关性特征处理")
        print("="*60)

        # 获取数值型特征
        numeric_cols = self.df_processed.select_dtypes(include=[np.number]).columns.tolist()

        # 排除客户号和目标变量
        exclude_cols = ['客户号']
        if '是否购买理财' in numeric_cols:
            exclude_cols.append('是否购买理财')

        numeric_cols = [col for col in numeric_cols if col not in exclude_cols]

        if len(numeric_cols) < 2:
            print("数值型特征不足，跳过相关性处理")
            return

        # 计算相关性矩阵
        corr_matrix = self.df_processed[numeric_cols].corr().abs()

        # 找出高相关性特征对
        high_corr_pairs = []
        removed_features = set()

        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                if corr_matrix.iloc[i, j] > self.correlation_threshold:
                    feature1 = corr_matrix.columns[i]
                    feature2 = corr_matrix.columns[j]
                    corr_value = corr_matrix.iloc[i, j]

                    high_corr_pairs.append({
                        'feature1': feature1,
                        'feature2': feature2,
                        'correlation': corr_value
                    })

                    # 删除其中一个特征（保留方差更大的）
                    var1 = self.df_processed[feature1].var()
                    var2 = self.df_processed[feature2].var()

                    if var1 >= var2 and feature2 not in removed_features:
                        removed_features.add(feature2)
                    elif feature1 not in removed_features:
                        removed_features.add(feature1)

        if removed_features:
            print(f"发现 {len(high_corr_pairs)} 对高相关性特征 (相关系数 > {self.correlation_threshold})")
            print(f"删除 {len(removed_features)} 个高相关性特征:")

            for feature in removed_features:
                print(f"  - {feature}")

            self.df_processed = self.df_processed.drop(columns=list(removed_features))
            self.processing_stats['dropped_columns'].extend(list(removed_features))

            self.processing_stats['correlation_handling'] = {
                'high_corr_pairs': high_corr_pairs,
                'removed_features': list(removed_features),
                'threshold': self.correlation_threshold
            }
        else:
            print(f"[OK] 未发现高相关性特征 (相关系数 > {self.correlation_threshold})")

        print(f"[OK] 高相关性特征处理完成")

    def create_derived_features(self):
        """创建衍生特征"""
        print("\n" + "="*60)
        print("7. 衍生特征创建")
        print("="*60)

        derived_features = []

        # 1. 存款相关比率特征
        if '存款总额年日均' in self.df_processed.columns:
            # 存款集中度（大额存单占比）
            if '大额存单年日均余额' in self.df_processed.columns:
                self.df_processed['大额存单占比'] = (
                    self.df_processed['大额存单年日均余额'] /
                    (self.df_processed['存款总额年日均'] + 1e-8)
                ).fillna(0)
                derived_features.append('大额存单占比')

            # 结构性存款占比
            if '结构性存款年日均余额' in self.df_processed.columns:
                self.df_processed['结构性存款占比'] = (
                    self.df_processed['结构性存款年日均余额'] /
                    (self.df_processed['存款总额年日均'] + 1e-8)
                ).fillna(0)
                derived_features.append('结构性存款占比')

        # 2. 交易活跃度特征
        if '近12月活期交易总笔数' in self.df_processed.columns and '近12月活期交易总金额' in self.df_processed.columns:
            # 平均单笔交易金额
            self.df_processed['平均单笔交易金额'] = (
                self.df_processed['近12月活期交易总金额'] /
                (self.df_processed['近12月活期交易总笔数'] + 1e-8)
            ).fillna(0)
            derived_features.append('平均单笔交易金额')

        # 3. 业务使用综合度
        business_cols = [col for col in self.df_processed.columns if '是否本年使用' in col or '是否本年签约' in col]
        if business_cols:
            self.df_processed['业务使用种类数'] = self.df_processed[business_cols].sum(axis=1)
            derived_features.append('业务使用种类数')

        # 4. 客户价值评分（基于多个维度）
        value_components = []

        if '存款总额年日均' in self.df_processed.columns:
            # 存款价值（标准化）
            deposit_value = self.df_processed['存款总额年日均'].fillna(0)
            deposit_value_norm = (deposit_value - deposit_value.min()) / (deposit_value.max() - deposit_value.min() + 1e-8)
            value_components.append(deposit_value_norm)

        if '近12月活期交易总金额' in self.df_processed.columns:
            # 交易价值（标准化）
            transaction_value = self.df_processed['近12月活期交易总金额'].fillna(0)
            transaction_value_norm = (transaction_value - transaction_value.min()) / (transaction_value.max() - transaction_value.min() + 1e-8)
            value_components.append(transaction_value_norm)

        if '业务使用种类数' in self.df_processed.columns:
            # 业务多样性（标准化）
            business_diversity = self.df_processed['业务使用种类数']
            business_diversity_norm = (business_diversity - business_diversity.min()) / (business_diversity.max() - business_diversity.min() + 1e-8)
            value_components.append(business_diversity_norm)

        if value_components:
            # 计算综合价值评分
            self.df_processed['客户价值评分'] = np.mean(value_components, axis=0)
            derived_features.append('客户价值评分')

        self.processing_stats['feature_engineering']['derived_features'] = derived_features

        if derived_features:
            print(f"[OK] 创建了 {len(derived_features)} 个衍生特征:")
            for feature in derived_features:
                print(f"  - {feature}")
        else:
            print("[OK] 未创建衍生特征")

        print(f"[OK] 衍生特征创建完成")

    def save_processed_data(self):
        """保存处理后的数据"""
        print("\n" + "="*60)
        print("8. 保存处理后数据")
        print("="*60)

        # 更新处理统计信息
        self.processing_stats['processed_shape'] = self.df_processed.shape

        # 保存主要数据文件（不带时间戳）
        main_file = 'processed_data.csv'
        self.df_processed.to_csv(main_file, index=False, encoding='utf-8-sig')
        print(f"[OK] 主要数据文件已保存: {main_file}")

        # 保存备份文件（带时间戳）
        backup_file = os.path.join(self.output_dir, f'processed_data_backup_{self.timestamp}.csv')
        self.df_processed.to_csv(backup_file, encoding='utf-8-sig')
        print(f"[OK] 备份数据文件已保存: {backup_file}")

        # 保存处理后数值特征统计
        numeric_cols = self.df_processed.select_dtypes(include=[np.number]).columns.tolist()
        if '客户号' in numeric_cols:
            numeric_cols.remove('客户号')

        if numeric_cols:
            numeric_stats = self.df_processed[numeric_cols].describe()
            stats_file = os.path.join(self.output_dir, f'{self.timestamp}_处理后数值特征统计.csv')
            numeric_stats.to_csv(stats_file, encoding='utf-8-sig')
            print(f"[OK] 处理后数值特征统计已保存: {stats_file}")

        # 保存处理统计信息
        import json
        stats_file = os.path.join(self.output_dir, f'{self.timestamp}_数据处理统计.json')
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.processing_stats, f, ensure_ascii=False, indent=2, default=str)
        print(f"[OK] 处理统计信息已保存: {stats_file}")

    def generate_comparison_plots(self):
        """生成处理前后对比图表"""
        print("\n" + "="*60)
        print("9. 生成对比图表")
        print("="*60)

        # 1. 数据形状对比
        self.plot_shape_comparison()

        # 2. 缺失值对比
        self.plot_missing_values_comparison()

        # 3. 数值特征分布对比（选择几个重要特征）
        self.plot_feature_distributions_comparison()

        print(f"[OK] 对比图表生成完成")

    def plot_shape_comparison(self):
        """绘制数据形状对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

        # 行数对比
        categories = ['原始数据', '处理后数据']
        row_counts = [self.processing_stats['original_shape'][0], self.processing_stats['processed_shape'][0]]

        bars1 = ax1.bar(categories, row_counts, color=['lightcoral', 'lightblue'], alpha=0.8)
        ax1.set_title('数据行数对比')
        ax1.set_ylabel('行数')
        ax1.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, count in zip(bars1, row_counts):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01,
                    f'{count:,}', ha='center', va='bottom', fontweight='bold')

        # 列数对比
        col_counts = [self.processing_stats['original_shape'][1], self.processing_stats['processed_shape'][1]]

        bars2 = ax2.bar(categories, col_counts, color=['lightcoral', 'lightblue'], alpha=0.8)
        ax2.set_title('数据列数对比')
        ax2.set_ylabel('列数')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, count in zip(bars2, col_counts):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01,
                    f'{count}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_数据形状对比.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"[OK] 数据形状对比图已保存: {filepath}")

    def plot_missing_values_comparison(self):
        """绘制缺失值对比图"""
        # 计算原始数据缺失值
        original_missing = self.df_original.isnull().sum()
        original_missing_cols = original_missing[original_missing > 0]

        # 计算处理后数据缺失值
        processed_missing = self.df_processed.isnull().sum()
        processed_missing_cols = processed_missing[processed_missing > 0]

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 缺失值总数对比
        categories = ['原始数据', '处理后数据']
        missing_totals = [original_missing.sum(), processed_missing.sum()]

        bars = ax1.bar(categories, missing_totals, color=['lightcoral', 'lightblue'], alpha=0.8)
        ax1.set_title('缺失值总数对比')
        ax1.set_ylabel('缺失值数量')
        ax1.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, count in zip(bars, missing_totals):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01,
                    f'{count:,}', ha='center', va='bottom', fontweight='bold')

        # 有缺失值的列数对比
        missing_col_counts = [len(original_missing_cols), len(processed_missing_cols)]

        bars2 = ax2.bar(categories, missing_col_counts, color=['lightcoral', 'lightblue'], alpha=0.8)
        ax2.set_title('有缺失值的列数对比')
        ax2.set_ylabel('列数')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, count in zip(bars2, missing_col_counts):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01,
                    f'{count}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_缺失值对比.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"[OK] 缺失值对比图已保存: {filepath}")

    def plot_feature_distributions_comparison(self):
        """绘制特征分布对比图"""
        # 选择几个重要的数值特征进行对比
        important_features = []

        # 选择存在于两个数据集中的数值特征
        original_numeric = self.df_original.select_dtypes(include=[np.number]).columns.tolist()
        processed_numeric = self.df_processed.select_dtypes(include=[np.number]).columns.tolist()

        common_features = list(set(original_numeric) & set(processed_numeric))

        # 排除客户号
        if '客户号' in common_features:
            common_features.remove('客户号')

        # 选择前6个特征进行对比
        important_features = common_features[:6]

        if not important_features:
            print("未找到可对比的数值特征")
            return

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()

        for i, feature in enumerate(important_features):
            ax = axes[i]

            # 获取原始和处理后的数据
            original_data = self.df_original[feature].dropna()
            processed_data = self.df_processed[feature].dropna()

            # 绘制直方图
            ax.hist(original_data, bins=50, alpha=0.6, label='原始数据', color='lightcoral', density=True)
            ax.hist(processed_data, bins=50, alpha=0.6, label='处理后数据', color='lightblue', density=True)

            ax.set_title(f'{feature}')
            ax.set_xlabel('数值')
            ax.set_ylabel('密度')
            ax.legend()
            ax.grid(True, alpha=0.3)

        # 隐藏多余的子图
        for i in range(len(important_features), len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_特征分布对比.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"[OK] 特征分布对比图已保存: {filepath}")

    def generate_processing_report(self):
        """生成数据处理报告"""
        print("\n" + "="*60)
        print("10. 生成处理报告")
        print("="*60)

        # 创建Markdown报告
        report_content = self.create_processing_report()

        # 保存报告
        report_file = os.path.join(self.output_dir, f'{self.timestamp}_数据处理报告.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"[OK] 数据处理报告已保存: {report_file}")

    def create_processing_report(self) -> str:
        """创建数据处理报告"""
        original_shape = self.processing_stats['original_shape']
        processed_shape = self.processing_stats['processed_shape']

        report = f"""# 数据预处理与特征工程报告

## 报告信息
- **生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **数据文件**: {self.data_file}
- **处理工具**: Python + Pandas + Scikit-learn

## 1. 数据处理概览

### 数据规模变化
- **原始数据**: {original_shape[0]:,} 行 x {original_shape[1]} 列
- **处理后数据**: {processed_shape[0]:,} 行 x {processed_shape[1]} 列
- **行数变化**: {processed_shape[0] - original_shape[0]:+,} ({(processed_shape[0] - original_shape[0])/original_shape[0]*100:+.2f}%)
- **列数变化**: {processed_shape[1] - original_shape[1]:+} ({(processed_shape[1] - original_shape[1])/original_shape[1]*100:+.2f}%)

## 2. 数据清洗

### 缺失值处理
- **删除的高缺失率列数**: {len([col for col in self.processing_stats['dropped_columns'] if '缺失率' in str(col)])}
- **填充缺失值的列数**: {len(self.processing_stats['missing_value_handling'])}

"""

        if self.processing_stats['missing_value_handling']:
            report += "### 缺失值填充详情\n"
            for col, info in self.processing_stats['missing_value_handling'].items():
                report += f"- **{col}**: {info['method']} 填充，处理 {info['missing_count']:,} 个缺失值 ({info['missing_rate']*100:.2f}%)\n"

        report += f"""
### 重复值处理
- **重复行数**: {self.processing_stats.get('duplicate_handling', {}).get('duplicate_count', 0)}
- **处理方式**: {self.processing_stats.get('duplicate_handling', {}).get('action', '无需处理')}

## 3. 特征工程

### 分类特征编码
"""

        if self.processing_stats['feature_encoding']:
            for col, info in self.processing_stats['feature_encoding'].items():
                report += f"- **{col}**: {info['method']} ({info['unique_count']} 个唯一值)\n"

        report += f"""
### 日期特征处理
"""

        if 'date_features' in self.processing_stats['feature_engineering']:
            date_info = self.processing_stats['feature_engineering']['date_features']
            report += f"- **原始列**: {date_info['original_column']}\n"
            report += f"- **新生成列**: {', '.join(date_info['new_columns'])}\n"
            report += f"- **参考日期**: {date_info['reference_date']}\n"

        report += f"""
### 衍生特征创建
"""

        if 'derived_features' in self.processing_stats['feature_engineering']:
            derived_features = self.processing_stats['feature_engineering']['derived_features']
            if derived_features:
                for feature in derived_features:
                    report += f"- **{feature}**\n"
            else:
                report += "- 未创建衍生特征\n"

        report += f"""
## 4. 数据质量优化

### 异常值处理
"""

        if self.processing_stats['outlier_handling']:
            for col, info in self.processing_stats['outlier_handling'].items():
                report += f"- **{col}**: {info['method']}，处理 {info['outlier_count']} 个异常值 ({info['outlier_rate']*100:.2f}%)\n"
        else:
            report += "- 未发现需要处理的异常值\n"

        report += f"""
### 高相关性特征处理
"""

        if 'correlation_handling' in self.processing_stats:
            corr_info = self.processing_stats['correlation_handling']
            report += f"- **删除特征数**: {len(corr_info['removed_features'])}\n"
            report += f"- **相关性阈值**: {corr_info['threshold']}\n"
            if corr_info['removed_features']:
                report += "- **删除的特征**: " + ", ".join(corr_info['removed_features']) + "\n"
        else:
            report += "- 未发现高相关性特征\n"

        report += f"""
## 5. 处理结果总结

### 删除的列
- **总删除列数**: {len(self.processing_stats['dropped_columns'])}
"""

        if self.processing_stats['dropped_columns']:
            report += "- **删除列列表**:\n"
            for col in self.processing_stats['dropped_columns']:
                report += f"  - {col}\n"

        report += f"""
### 数据质量改善
1. **完整性**: 消除了高缺失率特征，填充了重要特征的缺失值
2. **一致性**: 统一了分类特征编码，处理了重复数据
3. **准确性**: 识别并处理了异常值
4. **相关性**: 删除了高度相关的冗余特征

### 特征工程效果
1. **特征数量**: 从 {original_shape[1]} 个特征优化为 {processed_shape[1]} 个特征
2. **特征质量**: 增加了衍生特征，提升了特征的业务解释性
3. **数据可用性**: 所有特征均为数值型，可直接用于机器学习

## 6. 后续建议

1. **特征选择**: 建议使用统计方法和机器学习方法进一步筛选重要特征
2. **特征缩放**: 在模型训练前考虑对特征进行标准化或归一化
3. **特征验证**: 验证衍生特征的业务合理性和预测能力
4. **持续监控**: 定期检查数据质量，更新处理策略

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return report

    def run_preprocessing(self):
        """运行完整的数据预处理流程"""
        print("开始数据预处理与特征工程...")
        print("="*80)

        # 1. 加载数据
        if not self.load_data():
            return False

        # 2. 处理缺失值
        self.handle_missing_values()

        # 3. 处理重复值
        self.handle_duplicate_values()

        # 4. 编码分类特征
        self.encode_categorical_features()

        # 5. 处理日期特征
        self.handle_date_features()

        # 6. 处理异常值
        self.handle_outliers()

        # 7. 删除高相关性特征
        self.remove_highly_correlated_features()

        # 8. 创建衍生特征
        # self.create_derived_features()

        # 9. 保存处理后数据
        self.save_processed_data()

        # 10. 生成对比图表
        self.generate_comparison_plots()

        # 11. 生成处理报告
        self.generate_processing_report()

        print("\n" + "="*80)
        print("数据预处理与特征工程完成！")
        print(f"原始数据: {self.processing_stats['original_shape'][0]:,} 行 x {self.processing_stats['original_shape'][1]} 列")
        print(f"处理后数据: {self.processing_stats['processed_shape'][0]:,} 行 x {self.processing_stats['processed_shape'][1]} 列")
        print(f"所有结果已保存到: {self.output_dir}")
        print("="*80)

        return True

def main():
    """主函数"""
    # 创建数据预处理器
    preprocessor = DataPreprocessor()

    # 运行预处理
    success = preprocessor.run_preprocessing()

    if success:
        print("\n[OK] 模块2执行成功")
        return 0
    else:
        print("\n[ERROR] 模块2执行失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
