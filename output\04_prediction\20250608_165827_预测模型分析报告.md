# 潜在客户预测模型分析报告

## 报告信息
- **生成时间**: 2025年06月08日 16:59:19
- **模型类型**: EasyEnsemble + LIGHTGBM
- **数据来源**: labeled_samples.csv

## 1. 模型配置

### EasyEnsemble参数
- **子分类器类型**: LIGHTGBM
- **子分类器数量**: 10
- **正负样本比例**: 1:1
- **随机种子**: 42

### 数据概况
- **训练样本数**: 239,169
- **特征数量**: 11
- **未知客户数**: 332,172

## 2. 模型性能评估

### 主要指标
- **AUC**: 0.9925
- **F1 Score**: 0.5192
- **G-Mean**: 0.9677
- **MCC**: 0.5780

### 混淆矩阵
```
真实\预测    负样本    正样本
负样本       70265       937
正样本          28       521
```

### 分类报告
- **精确率 (Precision)**: 0.3573
- **召回率 (Recall)**: 0.9490
- **F1分数**: 0.5192


### 交叉验证结果 (5折)
- **AUC**: 0.9945 ± 0.0013
- **F1**: 0.5262 ± 0.0184
- **G_MEAN**: 0.9723 ± 0.0051
- **MCC**: 0.5852 ± 0.0129

## 3. 预测结果分析

### 预测统计
- **预测为正样本**: 94,369 个客户
- **预测为负样本**: 237,803 个客户
- **正样本比例**: 28.41%

### 购买概率分布
- **平均概率**: 0.2956
- **标准差**: 0.4211
- **最小值**: 0.0032
- **最大值**: 0.9972
- **中位数**: 0.0172

## 4. 使用的特征

### 最终选择的11个特征
 1. 企业网银-本年登录次数
 2. 存款总额年日均
 3. 近12月活期交易总金额
 4. 存贷款EVA
 5. 开户距今天数
 6. 近12月活期交易总笔数
 7. 近12月活期转出笔数
 8. 近12月活期转入笔数
 9. 当年月均交易笔数
10. 持有产品总数
11. 是否销户_是

## 5. EasyEnsemble技术说明

### 技术原理
EasyEnsemble是一种专门处理不平衡数据的集成学习方法：

1. **多次欠采样**: 对多数类进行多次随机欠采样，每次采样得到一个平衡的子数据集
2. **集成训练**: 在每个平衡子数据集上训练一个基分类器
3. **投票决策**: 将所有基分类器的预测结果进行集成，通过投票或平均得到最终预测

### 优势
- **保留信息**: 通过多次采样避免了信息丢失
- **提高泛化**: 集成多个分类器提高了模型的泛化能力
- **处理不平衡**: 有效解决了类别不平衡问题

### 本项目应用
- **正负样本比例**: 1:1
- **子分类器数量**: 10个
- **基分类器**: LIGHTGBM

## 6. 业务建议

### 高潜力客户识别
1. **重点关注**: 购买概率前10%的客户（约33,217个）
2. **营销策略**: 针对高概率客户制定个性化营销方案
3. **资源配置**: 优先将营销资源投入到高潜力客户

### 特征重要性洞察
基于模型特征重要性，以下因素对理财购买意愿影响较大：
1. **存款规模**: 存款总额年日均是最重要的预测因子
2. **数字化程度**: 企业网银使用频率反映客户活跃度
3. **交易活跃度**: 交易笔数和金额体现客户业务规模

### 营销建议
1. **精准营销**: 基于预测概率进行客户分层营销
2. **产品推荐**: 根据客户特征推荐合适的理财产品
3. **时机把握**: 在客户交易活跃期进行营销推广

## 7. 模型监控与更新

### 监控指标
- 定期评估模型预测准确性
- 监控客户行为变化对模型的影响
- 跟踪营销转化率

### 更新策略
- 建议每季度重新训练模型
- 根据新的客户数据更新特征工程
- 持续优化模型参数

---
*报告生成时间: 2025-06-08 16:59:19*
