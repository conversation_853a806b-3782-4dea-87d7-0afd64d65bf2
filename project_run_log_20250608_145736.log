2025-06-08 14:57:36,381 - INFO - ================================================================================
2025-06-08 14:57:36,381 - INFO - 对公理财客户分析项目启动
2025-06-08 14:57:36,382 - INFO - ================================================================================
2025-06-08 14:57:55,756 - INFO - 配置已保存到: run_config_20250608_145755.json
2025-06-08 14:57:55,756 - INFO - 开始执行所有模块
2025-06-08 14:57:55,760 - INFO - 开始执行模块1: 源数据探索性分析
2025-06-08 14:57:55,761 - INFO - 执行文件: 01_data_exploration.py
2025-06-08 14:58:01,201 - ERROR - 模块1执行失败，返回码: 1
2025-06-08 14:58:01,201 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\01_data_exploration.py", line 858, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\01_data_exploration.py", line 848, in main
    success = explorer.run_analysis()
  File "E:\2025\Ӫר\ƿͻȺ\060811\01_data_exploration.py", line 818, in run_analysis
    self.analyze_target_variable()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\01_data_exploration.py", line 127, in analyze_target_variable
    self.plot_target_distribution(target_counts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\01_data_exploration.py", line 168, in plot_target_distribution
    print(f"\u2713 Ŀֲͼѱ: {filepath}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

2025-06-08 14:58:01,202 - INFO - 输出: ʼ̽Է...
================================================================================
ڼ...
[OK] ݼسɹ: 334,001   49 

============================================================
1. ݻϢ
============================================================
״: (334001, 49)
: 334,001
: 49
ڴʹ: 276.84 MB
ͷֲ:
  float64: 27 
  int64: 14 
  object: 8 

============================================================
2. Ŀ
============================================================
Ŀ: Ƿ
ȡֲֵ:
  δ֪: 332,172 (99.45%)
  1: 1,829 (0.55%)

2025-06-08 14:58:04,813 - INFO - 开始执行模块2: 数据预处理与特征工程
2025-06-08 14:58:04,814 - INFO - 执行文件: 02_data_preprocessing.py
2025-06-08 14:58:08,434 - ERROR - 模块2执行失败，返回码: 1
2025-06-08 14:58:08,434 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\02_data_preprocessing.py", line 75, in load_data
    print(f"\u2713 ݼسɹ: {self.df_original.shape[0]:,}   {self.df_original.shape[1]} ")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\02_data_preprocessing.py", line 908, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\02_data_preprocessing.py", line 898, in main
    success = preprocessor.run_preprocessing()
  File "E:\2025\Ӫר\ƿͻȺ\060811\02_data_preprocessing.py", line 850, in run_preprocessing
    if not self.load_data():
           ~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\02_data_preprocessing.py", line 81, in load_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:58:08,434 - INFO - 输出: ʼԤ...
================================================================================
ڼ...

2025-06-08 14:58:09,292 - INFO - 开始执行模块3: PU学习与特征选择
2025-06-08 14:58:09,293 - INFO - 执行文件: 03_pu_learning_feature_selection.py
2025-06-08 14:58:13,125 - ERROR - 模块3执行失败，返回码: 1
2025-06-08 14:58:13,126 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\03_pu_learning_feature_selection.py", line 89, in load_data
    print(f"\u2713 ݼسɹ: {self.df.shape[0]:,}   {self.df.shape[1]} ")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\03_pu_learning_feature_selection.py", line 1079, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\03_pu_learning_feature_selection.py", line 1069, in main
    success = selector.run_pu_learning_feature_selection()
  File "E:\2025\Ӫר\ƿͻȺ\060811\03_pu_learning_feature_selection.py", line 1022, in run_pu_learning_feature_selection
    if not self.load_data():
           ~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\03_pu_learning_feature_selection.py", line 104, in load_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:58:13,126 - INFO - 输出: : SHAPδװSHAPҪԼ
ʼPUѧϰѡ...
================================================================================
ڼش...

2025-06-08 14:58:13,901 - INFO - 开始执行模块4: 潜在客户预测模型
2025-06-08 14:58:13,902 - INFO - 执行文件: 04_customer_prediction.py
2025-06-08 14:58:17,811 - ERROR - 模块4执行失败，返回码: 1
2025-06-08 14:58:17,811 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\04_customer_prediction.py", line 88, in load_data
    print(f"\u2713 עݼسɹ: {self.labeled_data.shape[0]:,}   {self.labeled_data.shape[1]} ")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\04_customer_prediction.py", line 852, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\04_customer_prediction.py", line 842, in main
    success = predictor.run_prediction_pipeline()
  File "E:\2025\Ӫר\ƿͻȺ\060811\04_customer_prediction.py", line 783, in run_prediction_pipeline
    if not self.load_data():
           ~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\04_customer_prediction.py", line 114, in load_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:58:17,812 - INFO - 输出: ʼǱڿͻԤģѵ...
================================================================================
ڼ...

2025-06-08 14:58:18,696 - INFO - 开始执行模块5: 客户聚类分析
2025-06-08 14:58:18,697 - INFO - 执行文件: 05_customer_clustering.py
2025-06-08 14:58:22,409 - ERROR - 模块5执行失败，返回码: 1
2025-06-08 14:58:22,410 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 80, in load_data
    print(f"\u2713 ݼسɹ: {self.df.shape[0]:,}   {self.df.shape[1]} ")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 1063, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 1053, in main
    success = clusterer.run_clustering_analysis()
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 998, in run_clustering_analysis
    if not self.load_data():
           ~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 96, in load_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:58:22,410 - INFO - 输出: ʼͻ...
================================================================================
ڼ...

2025-06-08 14:58:24,876 - INFO - 开始执行模块6: 结果报告与看板交付
2025-06-08 14:58:24,876 - INFO - 执行文件: 06_results_dashboard.py
2025-06-08 14:58:28,011 - ERROR - 模块6执行失败，返回码: 1
2025-06-08 14:58:28,012 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\06_results_dashboard.py", line 48, in load_all_data
    print(f"\u2713 ݼسɹ: {self.processed_data.shape}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\06_results_dashboard.py", line 443, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\06_results_dashboard.py", line 421, in main
    dashboard = DashboardGenerator()
  File "E:\2025\Ӫר\ƿͻȺ\060811\06_results_dashboard.py", line 41, in __init__
    self.load_all_data()
    ~~~~~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\06_results_dashboard.py", line 77, in load_all_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:58:28,940 - INFO - 项目执行完成，总耗时: 33.18秒
2025-06-08 14:58:28,941 - INFO - 成功: 0, 失败: 0, 跳过: 6
