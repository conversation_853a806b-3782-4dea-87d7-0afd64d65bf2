# 数据处理阶段行数减少分析报告

## 数据规模变化概览
- **原始数据**: 334,001 行 × 49 列
- **处理后数据**: 231,697 行 × 36 列
- **删除行数**: 102,304 行 (30.6%)
- **删除列数**: 13 列 (26.5%)

## 数据行数减少的主要原因

### 1. 异常值删除 (主要原因)
数据预处理模块使用Z-score方法检测异常值，当Z-score > 3时认为是异常值。
对于异常值比例 ≤ 10%的特征，采用**删除异常值行**的策略。

#### 异常值删除统计 (按影响程度排序):
1. **代扣税费-是否本年签约**: 删除 27,242 行 (9.74%)
2. **近12月活期交易总笔数**: 删除 8,423 行 (2.81%)
3. **代扣公积金-是否本年使用**: 删除 7,719 行 (2.68%)
4. **代发工资-是否本年签约**: 删除 6,229 行 (2.51%)
5. **近12月活期交易总金额**: 删除 5,820 行 (1.91%)
6. **企业网银-本年登录次数**: 删除 5,670 行 (1.73%)
7. **代发工资-是否本年使用**: 删除 3,812 行 (1.58%)
8. **近12月活期转入笔数**: 删除 3,791 行 (1.23%)
9. **近12月活期转出笔数**: 删除 3,063 行 (0.97%)
10. **融资年日均**: 删除 2,979 行 (0.90%)

**总计**: 约 102,304 行被删除 (考虑重叠)

### 2. 高缺失率列删除
删除了 15 个高缺失率列 (缺失率 > 95%):
- 大额存单年日均余额
- 结构性存款年日均余额
- 国际结算-本年交易量折美元
- 即期结售汇-本年交易量折美元
- 线上供应链-余额
- 线上供应链-本年发生额
- 国内保函-余额
- 银承贴现-余额
- 银承贴现-本年发生额
- 近12月代扣公积金金额
- 近12月代扣公积金笔数
- 近12月活期转出金额
- 开户年份
- 一般贷款本年发生额
- 近12月活期转入金额

### 3. 高相关性特征删除
删除了 4 个高相关性特征 (相关系数 > 0.99):
- 近12月活期转出金额
- 开户年份  
- 一般贷款本年发生额
- 近12月活期转入金额

## 数据删除的合理性分析

### ✅ 合理的删除
1. **高缺失率列删除**: 缺失率 > 95% 的列确实没有分析价值
2. **高相关性特征删除**: 避免多重共线性，提高模型稳定性
3. **部分异常值删除**: 对于明显的数据录入错误或极端值，删除是合理的

### ⚠️ 需要优化的删除策略
1. **布尔型特征的异常值删除**: 
   - "代扣税费-是否本年签约" 删除了 27,242 行 (9.74%)
   - "代扣公积金-是否本年使用" 删除了 7,719 行 (2.68%)
   - 这些是0/1二值特征，不应该有"异常值"

2. **交易笔数/金额的异常值删除**:
   - 大客户的高交易量可能被误判为异常值
   - 这些可能是有价值的高净值客户

## 建议的优化策略

### 1. 调整异常值检测策略
```python
# 对于布尔型特征，跳过异常值检测
boolean_features = [col for col in df.columns if '是否' in col]
numeric_cols = [col for col in numeric_cols if col not in boolean_features]

# 对于交易金额/笔数，使用更宽松的阈值
transaction_features = [col for col in df.columns if '交易' in col or '笔数' in col]
for col in transaction_features:
    # 使用99.5%分位数而不是Z-score
    threshold = df[col].quantile(0.995)
    # 截断而不是删除
```

### 2. 分层异常值处理
- **删除**: 明显的数据错误 (如负数金额)
- **截断**: 极端值但可能真实 (如超大交易金额)
- **保留**: 业务上合理的高值 (如VIP客户)

### 3. 保留更多样本
通过优化异常值处理策略，预计可以保留额外的 50,000-70,000 行数据，
将最终数据量提升至 280,000-300,000 行。

## 对模型的影响评估

### 正面影响
- 数据质量提升，减少噪声
- 特征间相关性降低
- 模型训练更稳定

### 负面影响  
- 样本量减少30.6%，可能影响模型泛化能力
- 可能删除了有价值的高净值客户样本
- 正样本比例可能发生变化

## 结论
当前的数据删除主要由异常值检测导致，删除了约10万行数据。
虽然提升了数据质量，但删除比例过高，建议优化异常值检测策略，
特别是对布尔型特征和交易类特征采用更合适的处理方法。
