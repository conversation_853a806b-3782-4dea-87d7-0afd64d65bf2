#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模块4：潜在客户预测模型
功能：EasyEnsemble不平衡数据处理、LightGBM模型训练、客户预测评分
作者：AI Assistant
创建时间：2025年1月
"""

import os
import sys
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import json
import pickle
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Union
from sklearn.model_selection import StratifiedKFold, train_test_split
from sklearn.metrics import (classification_report, confusion_matrix, roc_auc_score, 
                           f1_score, precision_recall_curve, roc_curve, matthews_corrcoef)
from sklearn.ensemble import AdaBoostClassifier
from sklearn.tree import DecisionTreeClassifier
import lightgbm as lgb
from imblearn.ensemble import EasyEnsembleClassifier
from collections import Counter

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10
plt.style.use('default')
warnings.filterwarnings('ignore')

class CustomerPredictor:
    """客户预测模型"""
    
    def __init__(self, ensemble_classifier: str = 'lightgbm', 
                 ensemble_n_estimators: int = 10, 
                 ensemble_ratio: str = '1:1'):
        """
        初始化客户预测器
        
        Args:
            ensemble_classifier: 子分类器类型 ('lightgbm', 'adaboost')
            ensemble_n_estimators: 子分类器数量
            ensemble_ratio: 正负样本比例 ('1:1', '1:2', '1:3', '1:4', '1:5')
        """
        self.ensemble_classifier = ensemble_classifier
        self.ensemble_n_estimators = ensemble_n_estimators
        self.ensemble_ratio = ensemble_ratio
        
        self.labeled_data = None
        self.unlabeled_data = None
        self.selected_features = []
        self.model = None
        
        self.output_dir = 'output/04_prediction'
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 评估结果存储
        self.evaluation_results = {}
        self.prediction_results = None
        
        # 可修改参数
        self.test_size = 0.3  # 测试集比例
        self.cv_folds = 5     # 交叉验证折数
        self.random_state = 42  # 随机种子
    
    def load_data(self) -> bool:
        """
        加载标注数据和特征列表
        
        Returns:
            bool: 是否成功加载数据
        """
        try:
            print("正在加载数据...")
            
            # 加载标注样本数据
            self.labeled_data = pd.read_csv('labeled_samples.csv', encoding='utf-8')
            print(f"[OK] 标注数据加载成功: {self.labeled_data.shape[0]:,} 行 x {self.labeled_data.shape[1]} 列")
            
            # 加载特征列表
            with open('final_features.json', 'r', encoding='utf-8') as f:
                features_data = json.load(f)
                self.selected_features = features_data['selected_features']
            print(f"[OK] 特征列表加载成功: {len(self.selected_features)} 个特征")
            
            # 加载处理后的完整数据（用于预测未知客户）
            full_data = pd.read_csv('processed_data.csv', encoding='utf-8')
            self.unlabeled_data = full_data[full_data['是否购买理财'] == '未知'].copy()
            print(f"[OK] 未知客户数据加载成功: {self.unlabeled_data.shape[0]:,} 行")
            
            # 检查数据完整性
            if 'label' not in self.labeled_data.columns:
                print("[ERROR] 标注数据中未找到 'label' 列")
                return False
            
            # 统计标注数据分布
            label_counts = self.labeled_data['label'].value_counts()
            print(f"标注数据分布:")
            for label, count in label_counts.items():
                print(f"  {label}: {count:,}")
            
            return True
        except Exception as e:
            print(f"[ERROR] 数据加载失败: {str(e)}")
            return False
    
    def prepare_training_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备训练数据
        
        Returns:
            Tuple[np.ndarray, np.ndarray]: 特征矩阵和标签向量
        """
        print("\n" + "="*60)
        print("1. 准备训练数据")
        print("="*60)
        
        # 提取特征和标签
        X = self.labeled_data[self.selected_features].values.astype(float)
        y = self.labeled_data['label'].values.astype(int)

        print(f"特征矩阵形状: {X.shape}")
        print(f"标签分布: {Counter(y)}")

        # 检查缺失值
        missing_count = np.isnan(X).sum()
        if missing_count > 0:
            print(f"警告: 发现 {missing_count} 个缺失值，将用0填充")
            X = np.nan_to_num(X, nan=0.0)
        
        return X, y
    
    def create_easy_ensemble_model(self):
        """创建EasyEnsemble模型"""
        print("\n" + "="*60)
        print("2. 创建EasyEnsemble模型")
        print("="*60)
        
        # 解析正负样本比例
        ratio_parts = self.ensemble_ratio.split(':')
        pos_ratio = int(ratio_parts[0])
        neg_ratio = int(ratio_parts[1])
        sampling_ratio = pos_ratio / neg_ratio
        
        print(f"配置参数:")
        print(f"  子分类器类型: {self.ensemble_classifier}")
        print(f"  子分类器数量: {self.ensemble_n_estimators}")
        print(f"  正负样本比例: {self.ensemble_ratio} (采样比例: {sampling_ratio:.2f})")
        
        # 创建基分类器
        if self.ensemble_classifier == 'lightgbm':
            # 使用LightGBM作为基分类器
            base_estimator = lgb.LGBMClassifier(
                objective='binary',
                boosting_type='gbdt',
                num_leaves=31,
                learning_rate=0.05,
                feature_fraction=0.9,
                bagging_fraction=0.8,
                bagging_freq=5,
                verbose=-1,
                random_state=self.random_state
            )
        else:
            # 使用AdaBoost作为基分类器
            base_estimator = AdaBoostClassifier(
                estimator=DecisionTreeClassifier(max_depth=3),
                n_estimators=50,
                learning_rate=1.0,
                random_state=self.random_state
            )
        
        # 创建EasyEnsemble分类器
        self.model = EasyEnsembleClassifier(
            estimator=base_estimator,
            n_estimators=self.ensemble_n_estimators,
            sampling_strategy=sampling_ratio,
            random_state=self.random_state,
            n_jobs=1  # 禁用并行处理以避免编码问题
        )
        
        print(f"[OK] EasyEnsemble模型创建完成")
    
    def train_and_evaluate_model(self, X: np.ndarray, y: np.ndarray):
        """
        训练和评估模型
        
        Args:
            X: 特征矩阵
            y: 标签向量
        """
        print("\n" + "="*60)
        print("3. 模型训练与评估")
        print("="*60)
        
        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=self.test_size, random_state=self.random_state, 
            stratify=y
        )
        
        print(f"训练集大小: {X_train.shape[0]:,}")
        print(f"测试集大小: {X_test.shape[0]:,}")
        print(f"训练集标签分布: {Counter(y_train)}")
        print(f"测试集标签分布: {Counter(y_test)}")
        
        # 训练模型
        print(f"\n开始训练EasyEnsemble模型...")
        self.model.fit(X_train, y_train)
        print(f"[OK] 模型训练完成")
        
        # 预测
        y_pred = self.model.predict(X_test)
        y_pred_proba = self.model.predict_proba(X_test)[:, 1]
        
        # 计算评估指标
        self.evaluation_results = {
            'auc': roc_auc_score(y_test, y_pred_proba),
            'f1': f1_score(y_test, y_pred),
            'mcc': matthews_corrcoef(y_test, y_pred),
            'classification_report': classification_report(y_test, y_pred, output_dict=True),
            'confusion_matrix': confusion_matrix(y_test, y_pred).tolist(),
            'y_test': y_test,
            'y_pred': y_pred,
            'y_pred_proba': y_pred_proba
        }
        
        # 计算G-Mean
        tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()
        sensitivity = tp / (tp + fn)  # 召回率
        specificity = tn / (tn + fp)  # 特异性
        g_mean = np.sqrt(sensitivity * specificity)
        self.evaluation_results['g_mean'] = g_mean
        
        # 打印评估结果
        print(f"\n模型评估结果:")
        print(f"  AUC: {self.evaluation_results['auc']:.4f}")
        print(f"  F1 Score: {self.evaluation_results['f1']:.4f}")
        print(f"  G-Mean: {self.evaluation_results['g_mean']:.4f}")
        print(f"  MCC: {self.evaluation_results['mcc']:.4f}")
        print(f"  Sensitivity (Recall): {sensitivity:.4f}")
        print(f"  Specificity: {specificity:.4f}")
        
        # 分类报告
        print(f"\n详细分类报告:")
        print(classification_report(y_test, y_pred))
    
    def perform_cross_validation(self, X: np.ndarray, y: np.ndarray):
        """
        执行分层交叉验证
        
        Args:
            X: 特征矩阵
            y: 标签向量
        """
        print("\n" + "="*60)
        print("4. 分层交叉验证")
        print("="*60)
        
        # 创建分层K折交叉验证
        skf = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
        
        cv_scores = {
            'auc': [],
            'f1': [],
            'g_mean': [],
            'mcc': []
        }
        
        print(f"执行 {self.cv_folds} 折交叉验证...")
        
        for fold, (train_idx, val_idx) in enumerate(skf.split(X, y), 1):
            print(f"  折 {fold}/{self.cv_folds}...")
            
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            
            # 训练模型
            fold_model = EasyEnsembleClassifier(
                estimator=self.model.estimator,
                n_estimators=self.ensemble_n_estimators,
                sampling_strategy=self.model.sampling_strategy,
                random_state=self.random_state + fold,  # 不同的随机种子
                n_jobs=1  # 禁用并行处理
            )
            
            fold_model.fit(X_train_fold, y_train_fold)
            
            # 预测和评估
            y_pred_fold = fold_model.predict(X_val_fold)
            y_pred_proba_fold = fold_model.predict_proba(X_val_fold)[:, 1]
            
            # 计算指标
            auc = roc_auc_score(y_val_fold, y_pred_proba_fold)
            f1 = f1_score(y_val_fold, y_pred_fold)
            mcc = matthews_corrcoef(y_val_fold, y_pred_fold)
            
            # 计算G-Mean
            tn, fp, fn, tp = confusion_matrix(y_val_fold, y_pred_fold).ravel()
            sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
            g_mean = np.sqrt(sensitivity * specificity)
            
            cv_scores['auc'].append(auc)
            cv_scores['f1'].append(f1)
            cv_scores['g_mean'].append(g_mean)
            cv_scores['mcc'].append(mcc)
        
        # 计算平均值和标准差
        cv_results = {}
        for metric, scores in cv_scores.items():
            cv_results[metric] = {
                'mean': np.mean(scores),
                'std': np.std(scores),
                'scores': scores
            }
        
        self.evaluation_results['cross_validation'] = cv_results
        
        print(f"\n交叉验证结果:")
        for metric, result in cv_results.items():
            print(f"  {metric.upper()}: {result['mean']:.4f} ± {result['std']:.4f}")
    
    def predict_unknown_customers(self):
        """对未知客户进行预测"""
        print("\n" + "="*60)
        print("5. 预测未知客户")
        print("="*60)
        
        # 准备未知客户的特征数据
        X_unknown = self.unlabeled_data[self.selected_features].values.astype(float)

        # 处理缺失值
        missing_count = np.isnan(X_unknown).sum()
        if missing_count > 0:
            print(f"处理 {missing_count} 个缺失值...")
            X_unknown = np.nan_to_num(X_unknown, nan=0.0)
        
        print(f"预测 {X_unknown.shape[0]:,} 个未知客户...")
        
        # 进行预测
        purchase_probabilities = self.model.predict_proba(X_unknown)[:, 1]
        purchase_predictions = self.model.predict(X_unknown)
        
        # 创建预测结果DataFrame
        self.prediction_results = pd.DataFrame({
            '客户号': self.unlabeled_data['客户号'].values,
            '购买概率': purchase_probabilities,
            '预测标签': purchase_predictions
        })
        
        # 添加特征值
        for feature in self.selected_features:
            self.prediction_results[feature] = self.unlabeled_data[feature].values
        
        # 按购买概率降序排序
        self.prediction_results = self.prediction_results.sort_values('购买概率', ascending=False)
        
        # 统计预测结果
        pred_counts = Counter(purchase_predictions)
        prob_stats = {
            'mean': np.mean(purchase_probabilities),
            'std': np.std(purchase_probabilities),
            'min': np.min(purchase_probabilities),
            'max': np.max(purchase_probabilities),
            'median': np.median(purchase_probabilities)
        }
        
        print(f"预测结果统计:")
        print(f"  预测为正样本: {pred_counts.get(1, 0):,}")
        print(f"  预测为负样本: {pred_counts.get(0, 0):,}")
        print(f"  购买概率统计:")
        print(f"    均值: {prob_stats['mean']:.4f}")
        print(f"    标准差: {prob_stats['std']:.4f}")
        print(f"    最小值: {prob_stats['min']:.4f}")
        print(f"    最大值: {prob_stats['max']:.4f}")
        print(f"    中位数: {prob_stats['median']:.4f}")
        
        self.evaluation_results['prediction_stats'] = {
            'prediction_counts': dict(pred_counts),
            'probability_stats': prob_stats
        }
        
        print(f"[OK] 未知客户预测完成")

    def generate_evaluation_plots(self):
        """生成模型评估图表"""
        print("\n" + "="*60)
        print("6. 生成评估图表")
        print("="*60)

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 混淆矩阵
        ax1 = axes[0, 0]
        cm = np.array(self.evaluation_results['confusion_matrix'])
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax1)
        ax1.set_title('混淆矩阵')
        ax1.set_xlabel('预测标签')
        ax1.set_ylabel('真实标签')

        # 2. ROC曲线
        ax2 = axes[0, 1]
        y_test = self.evaluation_results['y_test']
        y_pred_proba = self.evaluation_results['y_pred_proba']

        fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
        auc = self.evaluation_results['auc']

        ax2.plot(fpr, tpr, color='darkorange', lw=2,
                label=f'ROC曲线 (AUC = {auc:.4f})')
        ax2.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        ax2.set_xlim([0.0, 1.0])
        ax2.set_ylim([0.0, 1.05])
        ax2.set_xlabel('假正率 (FPR)')
        ax2.set_ylabel('真正率 (TPR)')
        ax2.set_title('ROC曲线')
        ax2.legend(loc="lower right")
        ax2.grid(True, alpha=0.3)

        # 3. Precision-Recall曲线
        ax3 = axes[1, 0]
        precision, recall, _ = precision_recall_curve(y_test, y_pred_proba)

        ax3.plot(recall, precision, color='blue', lw=2, label='PR曲线')
        ax3.set_xlabel('召回率 (Recall)')
        ax3.set_ylabel('精确率 (Precision)')
        ax3.set_title('Precision-Recall曲线')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 交叉验证结果
        ax4 = axes[1, 1]
        if 'cross_validation' in self.evaluation_results:
            cv_results = self.evaluation_results['cross_validation']
            metrics = list(cv_results.keys())
            means = [cv_results[metric]['mean'] for metric in metrics]
            stds = [cv_results[metric]['std'] for metric in metrics]

            x_pos = np.arange(len(metrics))
            bars = ax4.bar(x_pos, means, yerr=stds, capsize=5, alpha=0.8, color='lightgreen')
            ax4.set_xlabel('评估指标')
            ax4.set_ylabel('分数')
            ax4.set_title('交叉验证结果')
            ax4.set_xticks(x_pos)
            ax4.set_xticklabels([m.upper() for m in metrics])
            ax4.grid(True, alpha=0.3)

            # 添加数值标签
            for bar, mean, std in zip(bars, means, stds):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01,
                        f'{mean:.3f}±{std:.3f}', ha='center', va='bottom', fontsize=8)

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_模型评估结果.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"[OK] 模型评估图表已保存: {filepath}")

    def generate_prediction_plots(self):
        """生成预测结果图表"""
        print(f"\n生成预测结果图表...")

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 购买概率分布直方图
        ax1 = axes[0, 0]
        probabilities = self.prediction_results['购买概率']

        ax1.hist(probabilities, bins=50, alpha=0.8, color='skyblue', edgecolor='black')
        ax1.set_xlabel('购买概率')
        ax1.set_ylabel('客户数量')
        ax1.set_title('购买概率分布直方图')
        ax1.grid(True, alpha=0.3)

        # 添加统计信息
        mean_prob = probabilities.mean()
        median_prob = probabilities.median()
        ax1.axvline(mean_prob, color='red', linestyle='--', alpha=0.8, label=f'均值: {mean_prob:.4f}')
        ax1.axvline(median_prob, color='green', linestyle='--', alpha=0.8, label=f'中位数: {median_prob:.4f}')
        ax1.legend()

        # 2. 不同概率分段的客户数量
        ax2 = axes[0, 1]
        prob_bins = [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
        prob_labels = ['0-0.1', '0.1-0.2', '0.2-0.3', '0.3-0.4', '0.4-0.5',
                      '0.5-0.6', '0.6-0.7', '0.7-0.8', '0.8-0.9', '0.9-1.0']

        prob_counts = pd.cut(probabilities, bins=prob_bins, labels=prob_labels, include_lowest=True).value_counts()

        bars = ax2.bar(range(len(prob_counts)), prob_counts.values, alpha=0.8, color='lightcoral')
        ax2.set_xlabel('购买概率区间')
        ax2.set_ylabel('客户数量')
        ax2.set_title('不同概率分段的客户数量')
        ax2.set_xticks(range(len(prob_counts)))
        ax2.set_xticklabels(prob_counts.index, rotation=45)
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, count in zip(bars, prob_counts.values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01,
                    f'{count:,}', ha='center', va='bottom', fontsize=8)

        # 3. 高概率客户特征分析（前1000名）
        ax3 = axes[1, 0]
        top_customers = self.prediction_results.head(1000)

        # 选择几个重要特征进行分析
        important_features = ['存款总额年日均', '企业网银-本年登录次数', '当年月均交易笔数']
        available_features = [f for f in important_features if f in top_customers.columns]

        if available_features:
            feature_means = [top_customers[f].mean() for f in available_features]
            bars = ax3.bar(range(len(available_features)), feature_means, alpha=0.8, color='lightgreen')
            ax3.set_xlabel('特征')
            ax3.set_ylabel('平均值')
            ax3.set_title('高概率客户特征分析 (前1000名)')
            ax3.set_xticks(range(len(available_features)))
            ax3.set_xticklabels(available_features, rotation=45, ha='right')
            ax3.grid(True, alpha=0.3)

            # 添加数值标签
            for bar, mean in zip(bars, feature_means):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + mean*0.01,
                        f'{mean:.2f}', ha='center', va='bottom', fontsize=8)

        # 4. 预测标签分布饼图
        ax4 = axes[1, 1]
        pred_counts = self.prediction_results['预测标签'].value_counts()

        colors = ['lightblue', 'lightcoral']
        labels = ['预测为负样本', '预测为正样本']

        wedges, texts, autotexts = ax4.pie(pred_counts.values, labels=labels, autopct='%1.2f%%',
                                          colors=colors, startangle=90)
        ax4.set_title('预测标签分布')

        # 美化饼图文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')

        plt.tight_layout()

        # 保存图片
        filename = f'{self.timestamp}_预测结果分析.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"[OK] 预测结果图表已保存: {filepath}")

    def save_results(self):
        """保存预测结果和模型"""
        print("\n" + "="*60)
        print("7. 保存结果")
        print("="*60)

        # 保存预测结果（不带时间戳）
        prediction_file = 'prediction_scores.csv'
        self.prediction_results.to_csv(prediction_file, index=False, encoding='utf-8-sig')
        print(f"[OK] 预测结果已保存: {prediction_file}")

        # 保存备份文件（带时间戳）
        # backup_file = f'prediction_scores_backup_{self.timestamp}.csv'
        # self.prediction_results.to_csv(backup_file, index=False, encoding='utf-8-sig')
        # print(f"[OK] 备份预测结果已保存: {backup_file}")
        backup_file = os.path.join(self.output_dir, f'prediction_scores_backup_{self.timestamp}.csv')
        self.prediction_results.to_csv(backup_file, encoding='utf-8-sig')
        print(f"[OK] 备份数据文件已保存: {backup_file}")

        # 保存模型
        model_file = 'lightgbm_model.pkl'
        with open(model_file, 'wb') as f:
            pickle.dump(self.model, f)
        print(f"[OK] 模型已保存: {model_file}")

        # 保存评估结果
        eval_results = self.evaluation_results.copy()
        # 转换numpy数组为列表以便JSON序列化
        if 'y_test' in eval_results:
            eval_results['y_test'] = eval_results['y_test'].tolist()
        if 'y_pred' in eval_results:
            eval_results['y_pred'] = eval_results['y_pred'].tolist()
        if 'y_pred_proba' in eval_results:
            eval_results['y_pred_proba'] = eval_results['y_pred_proba'].tolist()

        # 转换所有numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, dict):
                return {str(k): convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj

        eval_results = convert_numpy_types(eval_results)

        eval_file = os.path.join(self.output_dir, f'{self.timestamp}_模型评估结果.json')
        with open(eval_file, 'w', encoding='utf-8') as f:
            json.dump(eval_results, f, ensure_ascii=False, indent=2, default=str)
        print(f"[OK] 评估结果已保存: {eval_file}")

        # 保存高潜力客户名单（购买概率前10%）
        # top_10_percent = int(len(self.prediction_results) * 0.1)
        # high_potential_customers = self.prediction_results.head(top_10_percent)

        high_potential_customers = self.prediction_results

        potential_file = os.path.join(self.output_dir, f'{self.timestamp}_预测名单.csv')
        high_potential_customers.to_csv(potential_file, index=False, encoding='utf-8-sig')
        print(f"[OK] 预测名单已保存: {potential_file} ({len(high_potential_customers):,} 个客户)")

    def generate_report(self):
        """生成预测分析报告"""
        print("\n" + "="*60)
        print("8. 生成分析报告")
        print("="*60)

        # 创建详细报告
        report_content = f"""# 潜在客户预测模型分析报告

## 报告信息
- **生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **模型类型**: EasyEnsemble + {self.ensemble_classifier.upper()}
- **数据来源**: labeled_samples.csv

## 1. 模型配置

### EasyEnsemble参数
- **子分类器类型**: {self.ensemble_classifier.upper()}
- **子分类器数量**: {self.ensemble_n_estimators}
- **正负样本比例**: {self.ensemble_ratio}
- **随机种子**: {self.random_state}

### 数据概况
- **训练样本数**: {len(self.labeled_data):,}
- **特征数量**: {len(self.selected_features)}
- **未知客户数**: {len(self.unlabeled_data):,}

## 2. 模型性能评估

### 主要指标
- **AUC**: {self.evaluation_results['auc']:.4f}
- **F1 Score**: {self.evaluation_results['f1']:.4f}
- **G-Mean**: {self.evaluation_results['g_mean']:.4f}
- **MCC**: {self.evaluation_results['mcc']:.4f}

### 混淆矩阵
```
真实\\预测    负样本    正样本
负样本      {self.evaluation_results['confusion_matrix'][0][0]:6d}    {self.evaluation_results['confusion_matrix'][0][1]:6d}
正样本      {self.evaluation_results['confusion_matrix'][1][0]:6d}    {self.evaluation_results['confusion_matrix'][1][1]:6d}
```

### 分类报告
- **精确率 (Precision)**: {self.evaluation_results['classification_report']['1']['precision']:.4f}
- **召回率 (Recall)**: {self.evaluation_results['classification_report']['1']['recall']:.4f}
- **F1分数**: {self.evaluation_results['classification_report']['1']['f1-score']:.4f}

"""

        # 添加交叉验证结果
        if 'cross_validation' in self.evaluation_results:
            cv_results = self.evaluation_results['cross_validation']
            report_content += f"""
### 交叉验证结果 ({self.cv_folds}折)
"""
            for metric, result in cv_results.items():
                report_content += f"- **{metric.upper()}**: {result['mean']:.4f} ± {result['std']:.4f}\n"

        # 添加预测结果
        pred_stats = self.evaluation_results['prediction_stats']
        report_content += f"""
## 3. 预测结果分析

### 预测统计
- **预测为正样本**: {pred_stats['prediction_counts'].get(1, 0):,} 个客户
- **预测为负样本**: {pred_stats['prediction_counts'].get(0, 0):,} 个客户
- **正样本比例**: {pred_stats['prediction_counts'].get(1, 0) / len(self.unlabeled_data) * 100:.2f}%

### 购买概率分布
- **平均概率**: {pred_stats['probability_stats']['mean']:.4f}
- **标准差**: {pred_stats['probability_stats']['std']:.4f}
- **最小值**: {pred_stats['probability_stats']['min']:.4f}
- **最大值**: {pred_stats['probability_stats']['max']:.4f}
- **中位数**: {pred_stats['probability_stats']['median']:.4f}

## 4. 使用的特征

### 最终选择的{len(self.selected_features)}个特征
"""

        for i, feature in enumerate(self.selected_features, 1):
            report_content += f"{i:2d}. {feature}\n"

        report_content += f"""
## 5. EasyEnsemble技术说明

### 技术原理
EasyEnsemble是一种专门处理不平衡数据的集成学习方法：

1. **多次欠采样**: 对多数类进行多次随机欠采样，每次采样得到一个平衡的子数据集
2. **集成训练**: 在每个平衡子数据集上训练一个基分类器
3. **投票决策**: 将所有基分类器的预测结果进行集成，通过投票或平均得到最终预测

### 优势
- **保留信息**: 通过多次采样避免了信息丢失
- **提高泛化**: 集成多个分类器提高了模型的泛化能力
- **处理不平衡**: 有效解决了类别不平衡问题

### 本项目应用
- **正负样本比例**: {self.ensemble_ratio}
- **子分类器数量**: {self.ensemble_n_estimators}个
- **基分类器**: {self.ensemble_classifier.upper()}

## 6. 业务建议

### 高潜力客户识别
1. **重点关注**: 购买概率前10%的客户（约{int(len(self.unlabeled_data) * 0.1):,}个）
2. **营销策略**: 针对高概率客户制定个性化营销方案
3. **资源配置**: 优先将营销资源投入到高潜力客户

### 特征重要性洞察
基于模型特征重要性，以下因素对理财购买意愿影响较大：
1. **存款规模**: 存款总额年日均是最重要的预测因子
2. **数字化程度**: 企业网银使用频率反映客户活跃度
3. **交易活跃度**: 交易笔数和金额体现客户业务规模

### 营销建议
1. **精准营销**: 基于预测概率进行客户分层营销
2. **产品推荐**: 根据客户特征推荐合适的理财产品
3. **时机把握**: 在客户交易活跃期进行营销推广

## 7. 模型监控与更新

### 监控指标
- 定期评估模型预测准确性
- 监控客户行为变化对模型的影响
- 跟踪营销转化率

### 更新策略
- 建议每季度重新训练模型
- 根据新的客户数据更新特征工程
- 持续优化模型参数

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        # 保存报告
        report_file = os.path.join(self.output_dir, f'{self.timestamp}_预测模型分析报告.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"[OK] 预测模型分析报告已保存: {report_file}")

    def run_prediction_pipeline(self):
        """运行完整的预测流程"""
        print("开始潜在客户预测模型训练...")
        print("="*80)

        # 1. 加载数据
        if not self.load_data():
            return False

        # 2. 准备训练数据
        X, y = self.prepare_training_data()

        # 3. 创建模型
        self.create_easy_ensemble_model()

        # 4. 训练和评估
        self.train_and_evaluate_model(X, y)

        # 5. 交叉验证
        self.perform_cross_validation(X, y)

        # 6. 预测未知客户
        self.predict_unknown_customers()

        # 7. 生成图表
        self.generate_evaluation_plots()
        self.generate_prediction_plots()

        # 8. 保存结果
        self.save_results()

        # 9. 生成报告
        self.generate_report()

        print("\n" + "="*80)
        print("潜在客户预测模型完成！")
        print(f"模型性能 - AUC: {self.evaluation_results['auc']:.4f}, F1: {self.evaluation_results['f1']:.4f}")
        print(f"预测客户数: {len(self.prediction_results):,}")
        print(f"所有结果已保存到: {self.output_dir}")
        print("="*80)

        return True

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='潜在客户预测模型')
    parser.add_argument('--ensemble_classifier', type=str, default='lightgbm',
                       choices=['lightgbm', 'adaboost'],
                       help='子分类器类型')
    parser.add_argument('--ensemble_n_estimators', type=int, default=10,
                       help='子分类器数量')
    parser.add_argument('--ensemble_ratio', type=str, default='1:1',
                       help='正负样本比例')

    args = parser.parse_args()

    # 创建客户预测器
    predictor = CustomerPredictor(
        ensemble_classifier=args.ensemble_classifier,
        ensemble_n_estimators=args.ensemble_n_estimators,
        ensemble_ratio=args.ensemble_ratio
    )

    # 运行预测流程
    success = predictor.run_prediction_pipeline()

    if success:
        print("\n[OK] 模块4执行成功")
        return 0
    else:
        print("\n[ERROR] 模块4执行失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
