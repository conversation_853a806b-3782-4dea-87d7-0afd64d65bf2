# PU学习与特征选择报告

## 报告信息
- **生成时间**: 2025年06月08日 16:20:38
- **数据文件**: processed_data.csv
- **PU学习方法**: spy

## 1. 数据概况

### 原始数据
- **总样本数**: 334,001
- **正样本数**: 1,829
- **未知样本数**: 332,172
- **原始特征数**: 35

### 标注数据
- **正样本数**: 1,829
- **负样本数**: 237,340
- **正负样本比例**: 1:129.76

## 2. PU学习方法

### SPY方法

- **间谍比例**: 0.15
- **方法描述**: 从正样本中随机选择15.0%作为间谍，混入未知样本中训练分类器

## 3. 特征选择流程

### 第一步：IV值粗筛
- **IV阈值**: 0.02
- **筛选前特征数**: 35
- **筛选后特征数**: 通过IV值筛选

### 第二步：多方法特征重要性评估
1. **LightGBM特征重要性**: 基于梯度提升决策树的特征重要性
2. **SHAP值**: 基于博弈论的特征贡献度分析
3. **排列重要性**: 基于特征打乱后性能下降的重要性评估

### 第三步：综合排名
- **最终特征数**: 11
- **排名策略**: 加权综合排名（LGB:40%, SHAP:40%, PERM:20%）

## 4. 最终选择的特征

### 前20个重要特征

 1. **企业网银-本年登录次数**
   - LightGBM: 0.3264
   - SHAP: 0.4806
   - 排列重要性: 0.3716

 2. **存款总额年日均**
   - LightGBM: 0.1469
   - SHAP: 0.1871
   - 排列重要性: 0.4653

 3. **近12月活期交易总金额**
   - LightGBM: 0.2173
   - SHAP: 0.1182
   - 排列重要性: 0.1288

 4. **存贷款EVA**
   - LightGBM: 0.0847
   - SHAP: 0.0422
   - 排列重要性: 0.0252

 5. **开户距今天数**
   - LightGBM: 0.0687
   - SHAP: 0.0377
   - 排列重要性: 0.0032

 6. **近12月活期交易总笔数**
   - LightGBM: 0.0402
   - SHAP: 0.0338
   - 排列重要性: 0.0023

 7. **近12月活期转出笔数**
   - LightGBM: 0.0354
   - SHAP: 0.0343
   - 排列重要性: -0.0004

 8. **近12月活期转入笔数**
   - LightGBM: 0.0326
   - SHAP: 0.0135
   - 排列重要性: 0.0038

 9. **当年月均交易笔数**
   - LightGBM: 0.0219
   - SHAP: 0.0286
   - 排列重要性: 0.0013

10. **持有产品总数**
   - LightGBM: 0.0251
   - SHAP: 0.0228
   - 排列重要性: -0.0011

11. **是否销户_是**
   - LightGBM: 0.0007
   - SHAP: 0.0013
   - 排列重要性: 0.0000

## 5. SHAP值分析解读

### SHAP方法说明
SHAP（SHapley Additive exPlanations）是基于博弈论的特征重要性分析方法，能够：
- 量化每个特征对模型预测的贡献度
- 提供特征影响的方向性（正向或负向）
- 保证所有特征贡献度之和等于模型输出与基准值的差异

### 关键SHAP发现
基于SHAP分析，以下特征对理财产品购买预测最为重要：

#### 前5个SHAP重要特征解读

**1. 企业网银-本年登录次数** (SHAP重要性: 0.4806)
- **业务含义**: 客户本年度企业网银登录次数
- **预测逻辑**: 网银使用频繁说明客户接受电子化服务，便于线上理财
- **营销策略**: 在网银平台设置理财产品推荐模块，精准营销

**2. 存款总额年日均** (SHAP重要性: 0.1871)
- **业务含义**: 客户年度日均存款余额，反映客户资金实力
- **预测逻辑**: 存款余额越高的客户，越有能力和意愿购买理财产品
- **营销策略**: 重点关注高净值存款客户，提供专属理财服务

**3. 近12月活期交易总金额** (SHAP重要性: 0.1182)
- **业务含义**: 客户近12个月活期账户交易总金额
- **预测逻辑**: 交易金额大说明客户资金流动性强，有理财需求
- **营销策略**: 针对高交易金额客户，推荐流动性好的理财产品

**4. 存贷款EVA** (SHAP重要性: 0.0422)
- **业务含义**: 客户特征指标
- **预测逻辑**: 该特征对理财产品购买预测有重要影响
- **营销策略**: 根据该特征细分客户，制定针对性营销策略

**5. 开户距今天数** (SHAP重要性: 0.0377)
- **业务含义**: 客户特征指标
- **预测逻辑**: 该特征对理财产品购买预测有重要影响
- **营销策略**: 根据该特征细分客户，制定针对性营销策略

### SHAP图表解读

#### 1. SHAP特征重要性汇总图（Bar Plot）
- **图表说明**: 显示各特征的平均绝对SHAP值，按重要性从高到低排序
- **解读方法**:
  - 横轴长度代表特征重要性大小
  - 数值越大，特征对模型预测的影响越大
  - 颜色区分不同特征，便于识别
- **业务应用**:
  - 识别最关键的客户特征
  - 指导营销策略制定的重点方向
  - 为客户画像提供科学依据

#### 2. SHAP特征影响分布图（Beeswarm Plot）
- **图表说明**: 展示特征值与SHAP值的关系，每个点代表一个样本
- **解读方法**:
  - 横轴为SHAP值（特征贡献度）
  - 纵轴为特征名称
  - 颜色表示特征值大小（红色=高值，蓝色=低值）
  - 点的分布显示特征影响的变异性
- **关键洞察**:
  - 红色点在右侧：高特征值促进正向预测（购买理财）
  - 蓝色点在左侧：低特征值促进负向预测（不购买理财）
  - 点的散布程度反映特征影响的稳定性
- **业务应用**:
  - 理解特征的正负向影响机制
  - 识别客户行为模式
  - 制定精准的客户分层策略

#### 3. SHAP瀑布图（Waterfall Plot）
- **图表说明**: 针对单个样本，展示各特征如何逐步影响最终预测
- **解读方法**:
  - 从基准值开始，每个特征贡献一个增量
  - 红色条：正向贡献（增加购买概率）
  - 蓝色条：负向贡献（降低购买概率）
  - 最终到达预测值
- **业务价值**:
  - 解释单个客户的预测结果
  - 为客户经理提供具体的营销话术
  - 支持个性化产品推荐

## 6. 关键发现

### 数据质量
- PU学习成功生成了237,340个负样本
- 正负样本在关键特征上表现出明显差异
- 特征选择有效降低了维度，提高了模型可解释性

### 特征特点
- 存款相关特征在区分正负样本中起重要作用
- 交易行为特征显示出较强的预测能力
- 业务使用情况特征有助于客户分类

### 业务洞察
- 高价值客户（存款余额大、交易频繁）更倾向于购买理财产品
- 企业网银使用频率是重要的行为指标
- 代发工资等基础业务使用情况反映客户粘性

## 6. 输出文件

### 主要文件
- `labeled_samples.csv`: 标注后的训练数据
- `final_features.json`: 最终选择的特征列表

### 分析文件
- 正负样本分布对比图
- 特征重要性对比图
- 统计对比结果CSV

---
*报告生成时间: 2025-06-08 16:20:38*
