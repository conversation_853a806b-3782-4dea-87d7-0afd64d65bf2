2025-06-08 14:50:56,753 - INFO - ================================================================================
2025-06-08 14:50:56,753 - INFO - 对公理财客户分析项目启动
2025-06-08 14:50:56,754 - INFO - ================================================================================
2025-06-08 14:52:02,065 - INFO - 配置已保存到: run_config_20250608_145202.json
2025-06-08 14:52:02,065 - INFO - 开始执行所有模块
2025-06-08 14:52:02,066 - INFO - 开始执行模块1: 源数据探索性分析
2025-06-08 14:52:02,066 - INFO - 执行文件: 01_data_exploration.py
2025-06-08 14:52:16,493 - ERROR - 模块1执行失败，返回码: 1
2025-06-08 14:52:16,494 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\01_data_exploration.py", line 64, in load_data
    print(f"\u2713 ݼسɹ: {self.df.shape[0]:,}   {self.df.shape[1]} ")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\01_data_exploration.py", line 843, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\01_data_exploration.py", line 833, in main
    success = explorer.run_analysis()
  File "E:\2025\Ӫר\ƿͻȺ\060811\01_data_exploration.py", line 796, in run_analysis
    if not self.load_data():
           ~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\01_data_exploration.py", line 67, in load_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:52:16,494 - INFO - 输出: ʼ̽Է...
================================================================================
ڼ...

2025-06-08 14:52:23,091 - INFO - 开始执行模块2: 数据预处理与特征工程
2025-06-08 14:52:23,091 - INFO - 执行文件: 02_data_preprocessing.py
2025-06-08 14:52:27,235 - ERROR - 模块2执行失败，返回码: 1
2025-06-08 14:52:27,235 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\02_data_preprocessing.py", line 74, in load_data
    print(f"\u2713 ݼسɹ: {self.df_original.shape[0]:,}   {self.df_original.shape[1]} ")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\02_data_preprocessing.py", line 907, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\02_data_preprocessing.py", line 897, in main
    success = preprocessor.run_preprocessing()
  File "E:\2025\Ӫר\ƿͻȺ\060811\02_data_preprocessing.py", line 849, in run_preprocessing
    if not self.load_data():
           ~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\02_data_preprocessing.py", line 80, in load_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:52:27,236 - INFO - 输出: ʼԤ...
================================================================================
ڼ...

2025-06-08 14:52:28,056 - INFO - 开始执行模块3: PU学习与特征选择
2025-06-08 14:52:28,056 - INFO - 执行文件: 03_pu_learning_feature_selection.py
2025-06-08 14:52:31,474 - ERROR - 模块3执行失败，返回码: 1
2025-06-08 14:52:31,474 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\03_pu_learning_feature_selection.py", line 88, in load_data
    print(f"\u2713 ݼسɹ: {self.df.shape[0]:,}   {self.df.shape[1]} ")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\03_pu_learning_feature_selection.py", line 1078, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\03_pu_learning_feature_selection.py", line 1068, in main
    success = selector.run_pu_learning_feature_selection()
  File "E:\2025\Ӫר\ƿͻȺ\060811\03_pu_learning_feature_selection.py", line 1021, in run_pu_learning_feature_selection
    if not self.load_data():
           ~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\03_pu_learning_feature_selection.py", line 103, in load_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:52:31,475 - INFO - 输出: : SHAPδװSHAPҪԼ
ʼPUѧϰѡ...
================================================================================
ڼش...

2025-06-08 14:52:32,388 - INFO - 开始执行模块4: 潜在客户预测模型
2025-06-08 14:52:32,388 - INFO - 执行文件: 04_customer_prediction.py
2025-06-08 14:52:35,895 - ERROR - 模块4执行失败，返回码: 1
2025-06-08 14:52:35,895 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\04_customer_prediction.py", line 87, in load_data
    print(f"\u2713 עݼسɹ: {self.labeled_data.shape[0]:,}   {self.labeled_data.shape[1]} ")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\04_customer_prediction.py", line 851, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\04_customer_prediction.py", line 841, in main
    success = predictor.run_prediction_pipeline()
  File "E:\2025\Ӫר\ƿͻȺ\060811\04_customer_prediction.py", line 782, in run_prediction_pipeline
    if not self.load_data():
           ~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\04_customer_prediction.py", line 113, in load_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:52:35,895 - INFO - 输出: ʼǱڿͻԤģѵ...
================================================================================
ڼ...

2025-06-08 14:52:36,917 - INFO - 开始执行模块5: 客户聚类分析
2025-06-08 14:52:36,917 - INFO - 执行文件: 05_customer_clustering.py
2025-06-08 14:52:39,708 - ERROR - 模块5执行失败，返回码: 1
2025-06-08 14:52:39,708 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 79, in load_data
    print(f"\u2713 ݼسɹ: {self.df.shape[0]:,}   {self.df.shape[1]} ")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 1062, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 1052, in main
    success = clusterer.run_clustering_analysis()
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 997, in run_clustering_analysis
    if not self.load_data():
           ~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 95, in load_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:52:39,708 - INFO - 输出: ʼͻ...
================================================================================
ڼ...

2025-06-08 14:52:40,765 - INFO - 开始执行模块6: 结果报告与看板交付
2025-06-08 14:52:40,765 - INFO - 执行文件: 06_results_dashboard.py
2025-06-08 14:52:43,572 - ERROR - 模块6执行失败，返回码: 1
2025-06-08 14:52:43,572 - ERROR - 错误信息: Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\06_results_dashboard.py", line 47, in load_all_data
    print(f"\u2713 ݼسɹ: {self.processed_data.shape}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\06_results_dashboard.py", line 442, in <module>
    sys.exit(main())
             ~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\06_results_dashboard.py", line 420, in main
    dashboard = DashboardGenerator()
  File "E:\2025\Ӫר\ƿͻȺ\060811\06_results_dashboard.py", line 40, in __init__
    self.load_all_data()
    ~~~~~~~~~~~~~~~~~~^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\06_results_dashboard.py", line 76, in load_all_data
    print(f"\u2717 ݼʧ: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence

2025-06-08 14:52:45,937 - INFO - 项目执行完成，总耗时: 43.87秒
2025-06-08 14:52:45,938 - INFO - 成功: 0, 失败: 0, 跳过: 6
