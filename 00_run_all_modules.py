#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
对公理财客户分析项目 - 整合运行文件
功能：按顺序执行所有6个模块，提供统一的项目入口
作者：AI Assistant
创建时间：2025年1月
"""

import os
import sys
import time
import logging
import subprocess
from datetime import datetime
from typing import Dict, List, Optional

class ProjectRunner:
    """项目整合运行器"""
    
    def __init__(self):
        """初始化运行器"""
        self.modules = {
            1: "01_data_exploration.py",
            2: "02_data_preprocessing.py", 
            3: "03_pu_learning_feature_selection.py",
            4: "04_customer_prediction.py",
            5: "05_customer_clustering.py",
            6: "06_results_dashboard.py"
        }
        
        self.module_names = {
            1: "源数据探索性分析",
            2: "数据预处理与特征工程",
            3: "PU学习与特征选择", 
            4: "潜在客户预测模型",
            5: "客户聚类分析",
            6: "结果报告与看板交付"
        }
        
        # 设置日志
        self.setup_logging()
        
        # 用户配置参数
        self.config = {
            'pu_method': 'spy',  # spy/isolation_forest/one_class_svm
            'ensemble_classifier': 'lightgbm',  # adaboost/lightgbm
            'ensemble_n_estimators': 10,  # 5-20
            'ensemble_ratio': '1:1',  # 1:1 到 1:5
            'clustering_method': 'both'  # kmeans/dbscan/both
        }
    
    def setup_logging(self):
        """设置日志记录"""
        log_filename = f"project_run_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("=" * 80)
        self.logger.info("对公理财客户分析项目启动")
        self.logger.info("=" * 80)
    
    def print_banner(self):
        """打印项目横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                          对公理财客户分析项目                                 ║
║                     Corporate Finance Customer Analysis                      ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  模块1: 源数据探索性分析                                                      ║
║  模块2: 数据预处理与特征工程                                                  ║
║  模块3: PU学习与特征选择                                                      ║
║  模块4: 潜在客户预测模型                                                      ║
║  模块5: 客户聚类分析                                                          ║
║  模块6: 结果报告与看板交付                                                    ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def get_user_config(self):
        """获取用户配置参数"""
        print("\n" + "="*60)
        print("参数配置")
        print("="*60)
        
        # PU学习方法选择
        print("\n1. PU学习负样本生成方法选择:")
        print("   a) spy - Spy Technique (推荐)")
        print("   b) isolation_forest - 孤立森林")
        print("   c) one_class_svm - 单类支持向量机")
        
        while True:
            choice = input("请选择方法 (a/b/c) [默认: a]: ").strip().lower()
            if choice == '' or choice == 'a':
                self.config['pu_method'] = 'spy'
                break
            elif choice == 'b':
                self.config['pu_method'] = 'isolation_forest'
                break
            elif choice == 'c':
                self.config['pu_method'] = 'one_class_svm'
                break
            else:
                print("无效选择，请重新输入")
        
        # EasyEnsemble配置
        print("\n2. EasyEnsemble配置:")
        print("   子分类器选择:")
        print("   a) lightgbm - LightGBM (推荐)")
        print("   b) adaboost - AdaBoost")
        
        while True:
            choice = input("请选择子分类器 (a/b) [默认: a]: ").strip().lower()
            if choice == '' or choice == 'a':
                self.config['ensemble_classifier'] = 'lightgbm'
                break
            elif choice == 'b':
                self.config['ensemble_classifier'] = 'adaboost'
                break
            else:
                print("无效选择，请重新输入")
        
        # 子分类器数量
        while True:
            try:
                n_est = input("子分类器数量 (5-20) [默认: 10]: ").strip()
                if n_est == '':
                    self.config['ensemble_n_estimators'] = 10
                    break
                n_est = int(n_est)
                if 5 <= n_est <= 20:
                    self.config['ensemble_n_estimators'] = n_est
                    break
                else:
                    print("请输入5-20之间的数字")
            except ValueError:
                print("请输入有效数字")
        
        # 正负样本比例
        print("\n   正负样本比例选择:")
        ratios = ['1:1', '1:2', '1:3', '1:4', '1:5']
        for i, ratio in enumerate(ratios, 1):
            print(f"   {i}) {ratio}")
        
        while True:
            try:
                choice = input("请选择比例 (1-5) [默认: 1]: ").strip()
                if choice == '':
                    self.config['ensemble_ratio'] = '1:1'
                    break
                choice = int(choice)
                if 1 <= choice <= 5:
                    self.config['ensemble_ratio'] = ratios[choice-1]
                    break
                else:
                    print("请输入1-5之间的数字")
            except ValueError:
                print("请输入有效数字")
        
        # 聚类算法选择
        print("\n3. 聚类算法选择:")
        print("   a) kmeans - K-Means聚类")
        print("   b) dbscan - DBSCAN聚类")
        print("   c) both - 两种算法都执行 (推荐)")
        
        while True:
            choice = input("请选择算法 (a/b/c) [默认: c]: ").strip().lower()
            if choice == '' or choice == 'c':
                self.config['clustering_method'] = 'both'
                break
            elif choice == 'a':
                self.config['clustering_method'] = 'kmeans'
                break
            elif choice == 'b':
                self.config['clustering_method'] = 'dbscan'
                break
            else:
                print("无效选择，请重新输入")
        
        # 显示配置摘要
        print("\n" + "="*60)
        print("配置摘要:")
        print("="*60)
        print(f"PU学习方法: {self.config['pu_method']}")
        print(f"子分类器: {self.config['ensemble_classifier']}")
        print(f"子分类器数量: {self.config['ensemble_n_estimators']}")
        print(f"正负样本比例: {self.config['ensemble_ratio']}")
        print(f"聚类算法: {self.config['clustering_method']}")
        
        confirm = input("\n确认配置并开始执行? (y/n) [默认: y]: ").strip().lower()
        if confirm == 'n':
            print("已取消执行")
            sys.exit(0)
    
    def save_config(self):
        """保存配置到文件"""
        import json
        config_file = f"run_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
        self.logger.info(f"配置已保存到: {config_file}")
    
    def run_module(self, module_num: int) -> bool:
        """
        运行指定模块
        
        Args:
            module_num: 模块编号
            
        Returns:
            bool: 是否成功执行
        """
        module_file = self.modules[module_num]
        module_name = self.module_names[module_num]
        
        self.logger.info(f"开始执行模块{module_num}: {module_name}")
        self.logger.info(f"执行文件: {module_file}")
        
        if not os.path.exists(module_file):
            self.logger.error(f"模块文件不存在: {module_file}")
            return False
        
        start_time = time.time()
        
        try:
            # 构建命令，传递配置参数
            cmd = [sys.executable, module_file]
            
            # 为需要配置的模块传递参数
            if module_num == 3:  # PU学习模块
                cmd.extend(['--pu_method', self.config['pu_method']])
            elif module_num == 4:  # 预测模块
                cmd.extend([
                    '--ensemble_classifier', self.config['ensemble_classifier'],
                    '--ensemble_n_estimators', str(self.config['ensemble_n_estimators']),
                    '--ensemble_ratio', self.config['ensemble_ratio']
                ])
            elif module_num == 5:  # 聚类模块
                cmd.extend(['--clustering_method', self.config['clustering_method']])
            
            # 执行模块
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                self.logger.info(f"模块{module_num}执行成功，耗时: {execution_time:.2f}秒")
                if result.stdout:
                    self.logger.info(f"输出: {result.stdout}")
                return True
            else:
                self.logger.error(f"模块{module_num}执行失败，返回码: {result.returncode}")
                if result.stderr:
                    self.logger.error(f"错误信息: {result.stderr}")
                if result.stdout:
                    self.logger.info(f"输出: {result.stdout}")
                return False
                
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"模块{module_num}执行异常，耗时: {execution_time:.2f}秒")
            self.logger.error(f"异常信息: {str(e)}")
            return False

    def handle_module_failure(self, module_num: int) -> str:
        """
        处理模块执行失败

        Args:
            module_num: 失败的模块编号

        Returns:
            str: 用户选择的操作 ('continue', 'skip', 'terminate')
        """
        module_name = self.module_names[module_num]

        print(f"\n{'='*60}")
        print(f"模块{module_num}执行失败: {module_name}")
        print(f"{'='*60}")
        print("请选择后续操作:")
        print("1. continue - 重试执行该模块")
        print("2. skip - 跳过该模块，继续执行后续模块")
        print("3. terminate - 终止整个项目执行")

        while True:
            choice = input("请选择操作 (1/2/3): ").strip()
            if choice == '1':
                return 'continue'
            elif choice == '2':
                return 'skip'
            elif choice == '3':
                return 'terminate'
            else:
                print("无效选择，请重新输入")

    def run_all_modules(self):
        """运行所有模块"""
        self.logger.info("开始执行所有模块")

        total_start_time = time.time()
        success_modules = []
        failed_modules = []
        skipped_modules = []

        for module_num in sorted(self.modules.keys()):
            module_name = self.module_names[module_num]

            print(f"\n{'='*80}")
            print(f"执行模块{module_num}: {module_name}")
            print(f"{'='*80}")

            while True:
                success = self.run_module(module_num)

                if success:
                    success_modules.append(module_num)
                    print(f"✓ 模块{module_num}执行成功")
                    break
                else:
                    action = self.handle_module_failure(module_num)

                    if action == 'continue':
                        print(f"重试执行模块{module_num}...")
                        continue
                    elif action == 'skip':
                        skipped_modules.append(module_num)
                        print(f"⚠ 跳过模块{module_num}")
                        break
                    elif action == 'terminate':
                        failed_modules.append(module_num)
                        print(f"✗ 项目执行终止于模块{module_num}")
                        self.print_execution_summary(
                            success_modules, failed_modules, skipped_modules,
                            time.time() - total_start_time
                        )
                        return

        total_execution_time = time.time() - total_start_time

        print(f"\n{'='*80}")
        print("所有模块执行完成")
        print(f"{'='*80}")

        self.print_execution_summary(
            success_modules, failed_modules, skipped_modules, total_execution_time
        )

    def print_execution_summary(self, success_modules: List[int],
                               failed_modules: List[int],
                               skipped_modules: List[int],
                               total_time: float):
        """
        打印执行摘要

        Args:
            success_modules: 成功执行的模块列表
            failed_modules: 失败的模块列表
            skipped_modules: 跳过的模块列表
            total_time: 总执行时间
        """
        print(f"\n{'='*80}")
        print("执行摘要")
        print(f"{'='*80}")

        print(f"总执行时间: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
        print(f"成功模块数: {len(success_modules)}")
        print(f"失败模块数: {len(failed_modules)}")
        print(f"跳过模块数: {len(skipped_modules)}")

        if success_modules:
            print(f"\n✓ 成功执行的模块:")
            for module_num in success_modules:
                print(f"   模块{module_num}: {self.module_names[module_num]}")

        if failed_modules:
            print(f"\n✗ 失败的模块:")
            for module_num in failed_modules:
                print(f"   模块{module_num}: {self.module_names[module_num]}")

        if skipped_modules:
            print(f"\n⚠ 跳过的模块:")
            for module_num in skipped_modules:
                print(f"   模块{module_num}: {self.module_names[module_num]}")

        # 记录到日志
        self.logger.info(f"项目执行完成，总耗时: {total_time:.2f}秒")
        self.logger.info(f"成功: {len(success_modules)}, 失败: {len(failed_modules)}, 跳过: {len(skipped_modules)}")

        print(f"\n详细日志已保存，请查看日志文件获取更多信息")

    def run(self):
        """主运行方法"""
        try:
            # 打印横幅
            self.print_banner()

            # 获取用户配置
            self.get_user_config()

            # 保存配置
            self.save_config()

            # 运行所有模块
            self.run_all_modules()

        except KeyboardInterrupt:
            print(f"\n\n用户中断执行")
            self.logger.info("用户中断执行")
        except Exception as e:
            print(f"\n\n项目执行出现异常: {str(e)}")
            self.logger.error(f"项目执行异常: {str(e)}")
            import traceback
            self.logger.error(f"异常堆栈: {traceback.format_exc()}")

def main():
    """主函数"""
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        sys.exit(1)

    # 检查必要文件
    if not os.path.exists('宽表.csv'):
        print("错误: 未找到数据文件 '宽表.csv'")
        sys.exit(1)

    # 创建并运行项目
    runner = ProjectRunner()
    runner.run()

if __name__ == "__main__":
    main()
