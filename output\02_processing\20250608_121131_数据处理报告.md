# 数据预处理与特征工程报告

## 报告信息
- **生成时间**: 2025年06月08日 12:11:44
- **数据文件**: 宽表.csv
- **处理工具**: Python + Pandas + Scikit-learn

## 1. 数据处理概览

### 数据规模变化
- **原始数据**: 334,001 行 × 49 列
- **处理后数据**: 232,314 行 × 42 列
- **行数变化**: -101,687 (-30.45%)
- **列数变化**: -7 (-14.29%)

## 2. 数据清洗

### 缺失值处理
- **删除的高缺失率列数**: 0
- **填充缺失值的列数**: 13

### 缺失值填充详情
- **首个账户开户日**: mode 填充，处理 1 个缺失值 (0.00%)
- **当年月均交易笔数**: median 填充，处理 121,279 个缺失值 (36.31%)
- **表内贷款余额年日均**: median 填充，处理 307,453 个缺失值 (92.05%)
- **融资年日均**: median 填充，处理 307,453 个缺失值 (92.05%)
- **存款总额年日均**: median 填充，处理 5,452 个缺失值 (1.63%)
- **贷款本年发生额**: median 填充，处理 314,011 个缺失值 (94.01%)
- **贷款余额**: median 填充，处理 314,011 个缺失值 (94.01%)
- **近12月活期转出金额**: median 填充，处理 141,700 个缺失值 (42.43%)
- **近12月活期转出笔数**: median 填充，处理 141,700 个缺失值 (42.43%)
- **近12月活期转入金额**: median 填充，处理 141,064 个缺失值 (42.23%)
- **近12月活期转入笔数**: median 填充，处理 141,064 个缺失值 (42.23%)
- **近12月代发工资金额**: median 填充，处理 307,847 个缺失值 (92.17%)
- **近12月代发工资笔数**: median 填充，处理 299,912 个缺失值 (89.79%)

### 重复值处理
- **重复行数**: 0
- **处理方式**: none

## 3. 特征工程

### 分类特征编码
- **现金管理-银企直连-是否本年使用**: label_encoding (2 个唯一值)
- **代扣公积金-是否本年使用**: label_encoding (2 个唯一值)
- **代扣税费-是否本年签约**: label_encoding (2 个唯一值)
- **代扣税费-是否本年使用**: label_encoding (2 个唯一值)
- **自助缴费-是否本年使用**: label_encoding (2 个唯一值)
- **收银宝-是否本年使用**: label_encoding (2 个唯一值)
- **代发工资-是否本年签约**: label_encoding (2 个唯一值)
- **代发工资-是否本年使用**: label_encoding (2 个唯一值)
- **是否销户**: one_hot_encoding (3 个唯一值)
- **是否有贷户**: label_encoding (2 个唯一值)
- **是否当年新开户**: label_encoding (2 个唯一值)
- **是否达标有效户**: label_encoding (2 个唯一值)
- **是否达标价值户**: label_encoding (2 个唯一值)
- **是否活跃客户**: label_encoding (2 个唯一值)
- **是否代发客户**: label_encoding (2 个唯一值)
- **是否同业**: label_encoding (2 个唯一值)

### 日期特征处理
- **原始列**: 首个账户开户日
- **新生成列**: 开户距今天数, 开户年份
- **参考日期**: 2024-12-31 00:00:00

### 衍生特征创建
- **平均单笔交易金额**
- **业务使用种类数**
- **客户价值评分**

## 4. 数据质量优化

### 异常值处理
- **存贷款EVA**: removal，处理 377 个异常值 (0.11%)
- **当年月均交易笔数**: removal，处理 679 个异常值 (0.20%)
- **表内贷款余额年日均**: removal，处理 762 个异常值 (0.23%)
- **融资年日均**: removal，处理 2965 个异常值 (0.89%)
- **存款总额年日均**: removal，处理 746 个异常值 (0.23%)
- **持有产品总数**: removal，处理 1452 个异常值 (0.44%)
- **企业网银-本年登录次数**: removal，处理 5671 个异常值 (1.73%)
- **一般贷款余额**: removal，处理 1537 个异常值 (0.48%)
- **一般贷款本年发生额**: removal，处理 1997 个异常值 (0.62%)
- **贷款本年发生额**: removal，处理 609 个异常值 (0.19%)
- **贷款余额**: removal，处理 1499 个异常值 (0.47%)
- **近12月活期转出金额**: removal，处理 721 个异常值 (0.23%)
- **近12月活期转出笔数**: removal，处理 3010 个异常值 (0.96%)
- **近12月活期转入金额**: removal，处理 2717 个异常值 (0.87%)
- **近12月活期转入笔数**: removal，处理 3748 个异常值 (1.21%)
- **近12月活期交易总金额**: removal，处理 5829 个异常值 (1.91%)
- **近12月活期交易总笔数**: removal，处理 8398 个异常值 (2.80%)
- **近12月代发工资金额**: removal，处理 93 个异常值 (0.03%)
- **近12月代发工资笔数**: removal，处理 1704 个异常值 (0.59%)
- **现金管理-银企直连-是否本年使用**: removal，处理 811 个异常值 (0.28%)
- **代扣公积金-是否本年使用**: removal，处理 7803 个异常值 (2.70%)
- **代扣税费-是否本年签约**: removal，处理 27404 个异常值 (9.76%)
- **自助缴费-是否本年使用**: removal，处理 2064 个异常值 (0.81%)
- **收银宝-是否本年使用**: removal，处理 2593 个异常值 (1.03%)
- **代发工资-是否本年签约**: removal，处理 6285 个异常值 (2.53%)
- **代发工资-是否本年使用**: removal，处理 3943 个异常值 (1.63%)
- **是否有贷户**: removal，处理 779 个异常值 (0.33%)
- **是否当年新开户**: removal，处理 2603 个异常值 (1.09%)
- **是否达标价值户**: removal，处理 2382 个异常值 (1.01%)
- **是否同业**: removal，处理 245 个异常值 (0.11%)
- **开户距今天数**: removal，处理 212 个异常值 (0.09%)
- **开户年份**: removal，处理 49 个异常值 (0.02%)

### 高相关性特征处理
- **删除特征数**: 1
- **相关性阈值**: 0.99
- **删除的特征**: 开户年份

## 5. 处理结果总结

### 删除的列
- **总删除列数**: 12
- **删除列列表**:
  - 大额存单年日均余额
  - 结构性存款年日均余额
  - 国际结算-本年交易量折美元
  - 即期结售汇-本年交易量折美元
  - 线上供应链-余额
  - 线上供应链-本年发生额
  - 国内保函-余额
  - 银承贴现-余额
  - 银承贴现-本年发生额
  - 近12月代扣公积金金额
  - 近12月代扣公积金笔数
  - 开户年份

### 数据质量改善
1. **完整性**: 消除了高缺失率特征，填充了重要特征的缺失值
2. **一致性**: 统一了分类特征编码，处理了重复数据
3. **准确性**: 识别并处理了异常值
4. **相关性**: 删除了高度相关的冗余特征

### 特征工程效果
1. **特征数量**: 从 49 个特征优化为 42 个特征
2. **特征质量**: 增加了衍生特征，提升了特征的业务解释性
3. **数据可用性**: 所有特征均为数值型，可直接用于机器学习

## 6. 后续建议

1. **特征选择**: 建议使用统计方法和机器学习方法进一步筛选重要特征
2. **特征缩放**: 在模型训练前考虑对特征进行标准化或归一化
3. **特征验证**: 验证衍生特征的业务合理性和预测能力
4. **持续监控**: 定期检查数据质量，更新处理策略

---
*报告生成时间: 2025-06-08 12:11:44*
