2025-06-08 14:59:59,559 - INFO - ================================================================================
2025-06-08 14:59:59,559 - INFO - 对公理财客户分析项目启动
2025-06-08 14:59:59,559 - INFO - ================================================================================
2025-06-08 15:00:28,238 - INFO - 配置已保存到: run_config_20250608_150028.json
2025-06-08 15:00:28,238 - INFO - 开始执行所有模块
2025-06-08 15:00:28,239 - INFO - 开始执行模块1: 源数据探索性分析
2025-06-08 15:00:28,239 - INFO - 执行文件: 01_data_exploration.py
2025-06-08 15:00:58,162 - INFO - 模块1执行成功，耗时: 29.92秒
2025-06-08 15:00:58,163 - INFO - 输出: ʼ̽Է...
================================================================================
ڼ...
[OK] ݼسɹ: 334,001  x 49 

============================================================
1. ݻϢ
============================================================
״: (334001, 49)
: 334,001
: 49
ڴʹ: 276.84 MB
ͷֲ:
  float64: 27 
  int64: 14 
  object: 8 

============================================================
2. Ŀ
============================================================
Ŀ: Ƿ
ȡֲֵ:
  δ֪: 332,172 (99.45%)
  1: 1,829 (0.55%)
[OK] Ŀֲͼѱ: output/01_EDA\20250608_150030_Ŀֲ.png

============================================================
3. ֵ
============================================================
ֵ: 40
[OK] ֵͳϢѱ: output/01_EDA\20250608_150030_ֵͳ.csv
[OK] ֲֵͼ1ҳѱ: output/01_EDA\20250608_150030_ֲֵ_1ҳ.png
[OK] ֲֵͼ2ҳѱ: output/01_EDA\20250608_150030_ֲֵ_2ҳ.png
[OK] ֲֵͼ3ҳѱ: output/01_EDA\20250608_150030_ֲֵ_3ҳ.png
[OK] ֲֵͼ4ҳѱ: output/01_EDA\20250608_150030_ֲֵ_4ҳ.png
[OK] ֵͼ1ҳѱ: output/01_EDA\20250608_150030_ֵͼ_1ҳ.png
[OK] ֵͼ2ҳѱ: output/01_EDA\20250608_150030_ֵͼ_2ҳ.png
[OK] ֵͼ3ҳѱ: output/01_EDA\20250608_150030_ֵͼ_3ҳ.png
[OK] ֵͼ4ҳѱ: output/01_EDA\20250608_150030_ֵͼ_4ҳ.png
[OK] ֵͼ5ҳѱ: output/01_EDA\20250608_150030_ֵͼ_5ҳ.png

============================================================
4. 
============================================================
ǿʶĲ: 17
: 0
: 17

ֽ-ֱ-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 331,172 (99.15%)
    1: 2,829 (0.85%)

Ƿ:
  Ψһֵ: 3
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 255,556 (76.51%)
    : 72,993 (21.85%)
    δ˻: 5,452 (1.63%)

ǷԾͻ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    1: 193,259 (57.86%)
    0: 140,742 (42.14%)

-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 302,615 (90.60%)
    1: 31,386 (9.40%)

ǷЧ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 234,222 (70.13%)
    : 99,779 (29.87%)

Ƿͬҵ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 333,554 (99.87%)
    1: 447 (0.13%)

Ƿͻ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 217,689 (65.18%)
    : 116,312 (34.82%)

-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 328,076 (98.23%)
    1: 5,925 (1.77%)

˰-ǷǩԼ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 297,489 (89.07%)
    1: 36,512 (10.93%)

Ƿ¿:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 296,240 (88.69%)
    : 37,761 (11.31%)

-ǷǩԼ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 301,931 (90.40%)
    1: 32,070 (9.60%)

ɷ-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 328,531 (98.36%)
    1: 5,470 (1.64%)

˰-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 240,843 (72.11%)
    1: 93,158 (27.89%)

۹-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 320,971 (96.10%)
    1: 13,030 (3.90%)

Ƿֵ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 320,220 (95.87%)
    : 13,781 (4.13%)

Ƿд:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 322,695 (96.61%)
    : 11,306 (3.39%)

Ƿ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    δ֪: 332,172 (99.45%)
    1: 1,829 (0.55%)
[OK] ֲͼ1ҳѱ: output/01_EDA\20250608_150030_ֲ_1ҳ.png
[OK] ֲͼ2ҳѱ: output/01_EDA\20250608_150030_ֲ_2ҳ.png
[OK] ֲͼ3ҳѱ: output/01_EDA\20250608_150030_ֲ_3ҳ.png

============================================================
5. ȱʧֵ
============================================================
ȱʧֵ: 24
ȱʧֵصǰ20:
  浥վ: 333,569 (99.87%)
  ڱ-: 332,595 (99.58%)
  -: 332,483 (99.55%)
  -귢: 332,483 (99.55%)
  ṹԴվ: 332,250 (99.48%)
  ڽۻ-꽻Ԫ: 330,779 (99.04%)
  ʽ-꽻Ԫ: 330,289 (98.89%)
  ϹӦ-귢: 329,129 (98.54%)
  ϹӦ-: 326,439 (97.74%)
  12´۹: 323,501 (96.86%)
  12´۹: 323,501 (96.86%)
  : 314,011 (94.01%)
  귢: 314,011 (94.01%)
  12´ʽ: 307,847 (92.17%)
  վ: 307,453 (92.05%)
  ڴվ: 307,453 (92.05%)
  12´ʱ: 299,912 (89.79%)
  12»ת: 141,700 (42.43%)
  12»ת: 141,700 (42.43%)
  12»ת: 141,064 (42.23%)
[OK] ȱʧֵͼѱ: output/01_EDA\20250608_150030_ȱʧֵͼ.png
[OK] ȱʧֵ״ͼѱ: output/01_EDA\20250608_150030_ȱʧֵ״ͼ.png

============================================================
6. Է
============================================================
[OK] Ծѱ: output/01_EDA\20250608_150030_Ծ.csv
[OK] ͼѱ: output/01_EDA\20250608_150030_ͼ.png

 17 Ը (|ϵ| > 0.8):
  12»ת <-> 12»ڽܽ: 1.000
  12»ת <-> 12»ڽܽ: 1.000
  12»ת <-> 12»ת: 1.000
  һ귢 <-> 귢: 1.000
  һ <-> : 1.000
  ʽ-꽻Ԫ <-> ϹӦ-귢: 0.998
  ʽ-꽻Ԫ <-> ϹӦ-: 0.995
  ڴվ <-> վ: 0.994
  12´۹ <-> 12´ʽ: 0.984
  վ <-> : 0.963
  ڴվ <-> : 0.961
  վ <-> һ: 0.961
  ڴվ <-> һ: 0.955
  ¾ױ <-> 12»ڽܱ: 0.910
  12»ת <-> 12»ڽܱ: 0.813
  ϹӦ- <-> ϹӦ-귢: 0.808
  - <-> -귢: 0.801

============================================================
7. ɷժҪ
============================================================
[OK] ժҪѱ: output/01_EDA\20250608_150030_̽.md
[OK] JSONѱ: output/01_EDA\20250608_150030_.json

================================================================================
̽Էɣ
нѱ浽: output/01_EDA
================================================================================

[OK] ģ1ִгɹ

2025-06-08 15:00:58,166 - INFO - 开始执行模块2: 数据预处理与特征工程
2025-06-08 15:00:58,166 - INFO - 执行文件: 02_data_preprocessing.py
2025-06-08 15:01:16,367 - INFO - 模块2执行成功，耗时: 18.20秒
2025-06-08 15:01:16,367 - INFO - 输出: ʼԤ...
================================================================================
ڼ...
[OK] ݼسɹ: 334,001  x 49 

============================================================
1. ȱʧֵ
============================================================
ȱʧʳ 95.0% : 11
ɾȱʧ:
  - 浥վ: 99.87%
  - ṹԴվ: 99.48%
  - ʽ-꽻Ԫ: 98.89%
  - ڽۻ-꽻Ԫ: 99.04%
  - ϹӦ-: 97.74%
  - ϹӦ-귢: 98.54%
  - ڱ-: 99.58%
  - -: 99.55%
  - -귢: 99.55%
  - 12´۹: 96.86%
  - 12´۹: 96.86%

ʣȱʧֵ: 13
  - ׸˻:  '1999/6/21 0:00'  1 ȱʧֵ
  - ¾ױ: 0 121279 ȱʧֵ
  - ڴվ: 0 307453 ȱʧֵ
  - վ: 0 307453 ȱʧֵ
  - ܶվ: 0 5452 ȱʧֵ
  - 귢: 0 314011 ȱʧֵ
  - : 0 314011 ȱʧֵ
  - 12»ת: 0 141700 ȱʧֵ
  - 12»ת: 0 141700 ȱʧֵ
  - 12»ת: 0 141064 ȱʧֵ
  - 12»ת: 0 141064 ȱʧֵ
  - 12´ʽ: 0 307847 ȱʧֵ
  - 12´ʱ: 0 299912 ȱʧֵ

[OK] ȱʧֵ
  ɾ: 11
  : 13

============================================================
2. ظֵ
============================================================
[OK] δظ

============================================================
3. 
============================================================
Ҫķ: 16
  - ֽ-ֱ-Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - ۹-Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - ˰-ǷǩԼ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - ˰-Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - ɷ-Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - -Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - -ǷǩԼ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - -Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - Ƿ: 3 Ψһֵ
     One-Hot Encoding:  2 
  - Ƿд: 2 Ψһֵ
     Label Encoding: ['' '']
  - Ƿ¿: 2 Ψһֵ
     Label Encoding: ['' '']
  - ǷЧ: 2 Ψһֵ
     Label Encoding: ['' '']
  - Ƿֵ: 2 Ψһֵ
     Label Encoding: ['' '']
  - ǷԾͻ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - Ƿͻ: 2 Ψһֵ
     Label Encoding: ['' '']
  - Ƿͬҵ: 2 Ψһֵ
     Label Encoding: ['0' '1']
[OK] 

============================================================
4. 
============================================================
: ׸˻
  [OK] : , 
  [OK] ɾԭʼ: ׸˻
[OK] 

============================================================
5. 쳣ֵ
============================================================
쳣ֵֵ: 36
  - EVA: ɾ 377 쳣ֵ (0.11%)
  - ¾ױ: ɾ 679 쳣ֵ (0.20%)
  - ڴվ: ɾ 763 쳣ֵ (0.23%)
  - վ: ɾ 2979 쳣ֵ (0.90%)
  - ܶվ: ɾ 746 쳣ֵ (0.23%)
  - вƷ: ɾ 1451 쳣ֵ (0.44%)
  - ҵ-¼: ɾ 5670 쳣ֵ (1.73%)
  - һ: ɾ 1528 쳣ֵ (0.48%)
  - һ귢: ɾ 1996 쳣ֵ (0.62%)
  - 귢: ɾ 843 쳣ֵ (0.27%)
  - : ɾ 1343 쳣ֵ (0.42%)
  - 12»ת: ɾ 721 쳣ֵ (0.23%)
  - 12»ת: ɾ 3063 쳣ֵ (0.97%)
  - 12»ת: ɾ 2712 쳣ֵ (0.87%)
  - 12»ת: ɾ 3791 쳣ֵ (1.23%)
  - 12»ڽܽ: ɾ 5820 쳣ֵ (1.91%)
  - 12»ڽܱ: ɾ 8423 쳣ֵ (2.81%)
  - 12´ʽ: ɾ 93 쳣ֵ (0.03%)
  - 12´ʱ: ɾ 2690 쳣ֵ (0.92%)
  - ֽ-ֱ-Ƿʹ: ɾ 795 쳣ֵ (0.28%)
  - ۹-Ƿʹ: ɾ 7719 쳣ֵ (2.68%)
  - ˰-ǷǩԼ: ɾ 27242 쳣ֵ (9.74%)
  - ɷ-Ƿʹ: ɾ 2054 쳣ֵ (0.81%)
  - -Ƿʹ: ɾ 2576 쳣ֵ (1.03%)
  - -ǷǩԼ: ɾ 6229 쳣ֵ (2.51%)
  - -Ƿʹ: ɾ 3812 쳣ֵ (1.58%)
  - Ƿд: ɾ 751 쳣ֵ (0.32%)
  - Ƿ¿: ɾ 2573 쳣ֵ (1.09%)
  - Ƿֵ: ɾ 2359 쳣ֵ (1.01%)
  - Ƿͬҵ: ɾ 245 쳣ֵ (0.11%)
  - : ɾ 209 쳣ֵ (0.09%)
  - : ɾ 52 쳣ֵ (0.02%)
[OK] 쳣ֵɣ 32 

============================================================
6. 
============================================================
 4 Ը (ϵ > 0.99)
ɾ 4 :
  - 12»ת
  - 
  - һ귢
  - 12»ת
[OK] 

============================================================
8. 洦
============================================================
[OK] Ҫļѱ: processed_data.csv
[OK] ļѱ: processed_data_backup_20250608_150100.csv
[OK] ֵͳѱ: output/02_processing\20250608_150100_ֵͳ.csv
[OK] ͳϢѱ: output/02_processing\20250608_150100_ݴͳ.json

============================================================
9. ɶԱͼ
============================================================
[OK] ״Աͼѱ: output/02_processing\20250608_150100_״Ա.png
[OK] ȱʧֵԱͼѱ: output/02_processing\20250608_150100_ȱʧֵԱ.png
[OK] ֲԱͼѱ: output/02_processing\20250608_150100_ֲԱ.png
[OK] Աͼ

============================================================
10. ɴ
============================================================
[OK] ݴѱ: output/02_processing\20250608_150100_ݴ.md

================================================================================
Ԥɣ
ԭʼ: 334,001  x 49 
: 231,697  x 36 
нѱ浽: output/02_processing
================================================================================

[OK] ģ2ִгɹ

2025-06-08 15:01:16,370 - INFO - 开始执行模块3: PU学习与特征选择
2025-06-08 15:01:16,370 - INFO - 执行文件: 03_pu_learning_feature_selection.py
2025-06-08 15:01:36,904 - INFO - 模块3执行成功，耗时: 20.53秒
2025-06-08 15:01:36,905 - INFO - 输出: : SHAPδװSHAPҪԼ
ʼPUѧϰѡ...
================================================================================
ڼش...
[OK] ݼسɹ: 231,697  x 36 
Ŀֲ:
  δ֪: 231,328
  1: 369

============================================================
1. ׼PUѧϰ
============================================================
: 369
δ֪: 231,328
: 34

============================================================
2. 
============================================================
ʹ÷: spy

ʹSpyɸ...
: 55
Ԥֵ: 0.0001
ɵĸ: 150258

עݼ:
  : 369
  : 150,258
  ܼ: 150,627
  : 1:407.20

============================================================
3. ֲ
============================================================
[OK] ֲԱͼѱ: output/03_feature_selection\20250608_150119_ֲԱ.png

ͳƶԱ:
  ڴվ:
    ֵ: 0.0000, ׼: 0.0000
    ֵ: 4877.6585, ׼: 103875.1215
    ֵ: 487765847715.5958
  ܶվ:
    ֵ: 104053.8092, ׼: 119201.7401
    ֵ: 273.5005, ׼: 16854.5314
    ֵ: 0.9974
  һ:
    ֵ: 0.0000, ׼: 0.0000
    ֵ: 0.0000, ׼: 0.0000
    ֵ: 0.0000
  ¾ױ:
    ֵ: 5.0054, ׼: 4.7577
    ֵ: 0.2197, ׼: 1.0754
    ֵ: 0.9561
  12»ת:
    ֵ: 38.0434, ׼: 39.6760
    ֵ: 1.5711, ׼: 6.9578
    ֵ: 0.9587
  12»ת:
    ֵ: 21.2493, ׼: 23.8432
    ֵ: 1.2807, ׼: 6.5598
    ֵ: 0.9397

[OK] ͳƶԱȽѱ: output/03_feature_selection\20250608_150119_ͳƶԱ.csv

============================================================
4. IVֵ㣨ɸ
============================================================
IVֵɣǰ20:
   1. 12»ڽܽ: 4.7848
   2. 12»ڽܱ: 4.3183
   3. 12»ת: 3.4718
   4. 12»ת: 3.1785
   5. EVA: 2.4427
   6. ܶվ: 2.3907
   7. ҵ-¼: 2.1668
   8. вƷ: 1.5853
   9. Ƿ_: 1.2792
  10. : 0.7647
  11. Ƿ_δ˻: 0.0003
  12. ¾ױ: 0.0000
  13. ڴվ: 0.0000
  14. վ: 0.0000
  15. һ: 0.0000
  16. 귢: 0.0000
  17. : 0.0000
  18. 12´ʽ: 0.0000
  19. 12´ʱ: 0.0000
  20. ֽ-ֱ-Ƿʹ: 0.0000

IVֵɸѡ:
  ֵ: 0.02
  ɸѡǰ: 34
  ɸѡ: 10
  ɾ: 24

============================================================
5. Ҫѡ
============================================================

ʹLightGBMҪ...
[OK] LightGBMҪԼ

SHAPҪԼ㣨SHAPδװ

ʹҪԼҪ...
[OK] ҪԼ

============================================================
6. ۺ
============================================================
ۺǰ30:
   1. ܶվ
      ۺ: 1.40
      LGB: 0.4325, SHAP: 0.1000, PERM: 0.4939
   2. ҵ-¼
      ۺ: 2.80
      LGB: 0.1763, SHAP: 0.1000, PERM: 0.4482
   3. EVA
      ۺ: 3.80
      LGB: 0.0703, SHAP: 0.1000, PERM: 0.0044
   4. 12»ڽܽ
      ۺ: 5.00
      LGB: 0.0777, SHAP: 0.1000, PERM: 0.0199
   5. 
      ۺ: 6.00
      LGB: 0.0930, SHAP: 0.1000, PERM: 0.0119
   6. вƷ
      ۺ: 6.40
      LGB: 0.0138, SHAP: 0.1000, PERM: 0.0024
   7. 12»ת
      ۺ: 6.40
      LGB: 0.0336, SHAP: 0.1000, PERM: 0.0070
   8. 12»ڽܱ
      ۺ: 6.60
      LGB: 0.0599, SHAP: 0.1000, PERM: 0.0111
   9. 12»ת
      ۺ: 7.00
      LGB: 0.0418, SHAP: 0.1000, PERM: 0.0012
  10. Ƿ_
      ۺ: 9.60
      LGB: 0.0012, SHAP: 0.1000, PERM: 0.0000

ҪԶԱͼ...
[OK] ҪԶԱͼѱ: output/03_feature_selection\20250608_150119_ҪԶԱ.png

============================================================
7. 
============================================================
[OK] עѱ: labeled_samples.csv
[OK] ݱעѱ: labeled_samples_backup_20250608_150119.csv
[OK] бѱ: final_features.json
[OK] бѱ: final_features_backup_20250608_150119.json
[OK] ϸѡѱ: output/03_feature_selection\20250608_150119_ѡϸ.json

============================================================
8. ɷ
============================================================
[OK] PUѧϰѡ񱨸ѱ: output/03_feature_selection\20250608_150119_PUѧϰѡ񱨸.md

================================================================================
PUѧϰѡɣ
ѡ: 10
ע: 150,627
нѱ浽: output/03_feature_selection
================================================================================

[OK] ģ3ִгɹ

2025-06-08 15:01:36,906 - INFO - 开始执行模块4: 潜在客户预测模型
2025-06-08 15:01:36,906 - INFO - 执行文件: 04_customer_prediction.py
2025-06-08 15:03:12,176 - INFO - 模块4执行成功，耗时: 95.27秒
2025-06-08 15:03:12,176 - INFO - 输出: ʼǱڿͻԤģѵ...
================================================================================
ڼ...
[OK] עݼسɹ: 150,627  x 37 
[OK] бسɹ: 10 
[OK] δ֪ͻݼسɹ: 231,328 
עݷֲ:
  0: 150,258
  1: 369

============================================================
1. ׼ѵ
============================================================
״: (150627, 10)
ǩֲ: Counter({np.int64(0): 150258, np.int64(1): 369})

============================================================
2. EasyEnsembleģ
============================================================
ò:
  ӷ: adaboost
  ӷ: 20
  : 1:1 (: 1.00)
[OK] EasyEnsembleģʹ

============================================================
3. ģѵ
============================================================
ѵС: 105,438
ԼС: 45,189
ѵǩֲ: Counter({np.int64(0): 105180, np.int64(1): 258})
Լǩֲ: Counter({np.int64(0): 45078, np.int64(1): 111})

ʼѵEasyEnsembleģ...
[OK] ģѵ

ģ:
  AUC: 0.9992
  F1 Score: 0.3354
  G-Mean: 0.9907
  MCC: 0.4450
  Sensitivity (Recall): 0.9910
  Specificity: 0.9904

ϸ౨:
              precision    recall  f1-score   support

           0       1.00      0.99      1.00     45078
           1       0.20      0.99      0.34       111

    accuracy                           0.99     45189
   macro avg       0.60      0.99      0.67     45189
weighted avg       1.00      0.99      0.99     45189


============================================================
4. ֲ㽻֤
============================================================
ִ 5 ۽֤...
   1/5...
   2/5...
   3/5...
   4/5...
   5/5...

֤:
  AUC: 0.9975  0.0018
  F1: 0.3724  0.0437
  G_MEAN: 0.9796  0.0037
  MCC: 0.4701  0.0350

============================================================
5. Ԥδ֪ͻ
============================================================
Ԥ 231,328 δ֪ͻ...
Ԥͳ:
  ԤΪ: 70,786
  ԤΪ: 160,542
  ͳ:
    ֵ: 0.4034
    ׼: 0.2345
    Сֵ: 0.1709
    ֵ: 0.8574
    λ: 0.2334
[OK] δ֪ͻԤ

============================================================
6. ͼ
============================================================
[OK] ģͼѱ: output/04_prediction\20250608_150140_ģ.png

Ԥͼ...
[OK] Ԥͼѱ: output/04_prediction\20250608_150140_Ԥ.png

============================================================
7. 
============================================================
[OK] Ԥѱ: prediction_scores.csv
[OK] Ԥѱ: prediction_scores_backup_20250608_150140.csv
[OK] ģѱ: lightgbm_model.pkl
[OK] ѱ: output/04_prediction\20250608_150140_ģ.json
[OK] Ԥѱ: output/04_prediction\20250608_150140_Ԥ.csv (231,328 ͻ)

============================================================
8. ɷ
============================================================
[OK] Ԥģͷѱ: output/04_prediction\20250608_150140_Ԥģͷ.md

================================================================================
ǱڿͻԤģɣ
ģ - AUC: 0.9992, F1: 0.3354
Ԥͻ: 231,328
нѱ浽: output/04_prediction
================================================================================

[OK] ģ4ִгɹ

2025-06-08 15:03:12,178 - INFO - 开始执行模块5: 客户聚类分析
2025-06-08 15:03:12,178 - INFO - 执行文件: 05_customer_clustering.py
2025-06-08 15:09:46,162 - INFO - 用户中断执行
