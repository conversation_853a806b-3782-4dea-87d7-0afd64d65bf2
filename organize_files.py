#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件组织脚本
将备份文件移动到对应的子目录
"""

import os
import shutil
import glob

def organize_backup_files():
    """组织备份文件"""
    print("开始组织备份文件...")
    
    # 定义文件移动规则
    move_rules = {
        'processed_data_backup_*.csv': 'output/02_processing/',
        'labeled_samples_backup_*.csv': 'output/03_feature_selection/',
        'final_features_backup_*.json': 'output/03_feature_selection/',
        'prediction_scores_backup_*.csv': 'output/04_prediction/',
        'clustering_results_backup_*.csv': 'output/05_clustering/'
    }
    
    moved_count = 0
    
    for pattern, target_dir in move_rules.items():
        # 确保目标目录存在
        os.makedirs(target_dir, exist_ok=True)
        
        # 查找匹配的文件
        files = glob.glob(pattern)
        
        for file_path in files:
            if os.path.exists(file_path):
                target_path = os.path.join(target_dir, os.path.basename(file_path))
                
                try:
                    # 如果目标文件已存在，先删除
                    if os.path.exists(target_path):
                        os.remove(target_path)
                    
                    # 移动文件
                    shutil.move(file_path, target_path)
                    print(f"移动: {file_path} -> {target_path}")
                    moved_count += 1
                    
                except Exception as e:
                    print(f"移动失败 {file_path}: {str(e)}")
    
    print(f"\n文件组织完成! 移动了 {moved_count} 个备份文件")
    
    # 显示主目录剩余的重要文件
    print("\n主目录保留的主要文件:")
    main_files = [
        'processed_data.csv',
        'labeled_samples.csv', 
        'final_features.json',
        'prediction_scores.csv',
        'clustering_results.csv',
        'lightgbm_model.pkl'
    ]
    
    for file_name in main_files:
        if os.path.exists(file_name):
            print(f"  [OK] {file_name}")
        else:
            print(f"  [MISSING] {file_name}")

def main():
    """主函数"""
    organize_backup_files()

if __name__ == "__main__":
    main()
