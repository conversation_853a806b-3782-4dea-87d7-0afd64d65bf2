# 对公理财客户数据探索性分析报告

## 报告信息
- **生成时间**: 2025年06月08日 16:15:49
- **数据文件**: 宽表.csv
- **分析工具**: Python + Pandas + Matplotlib + Seaborn

## 1. 数据基本信息

### 数据规模
- **总行数**: 334,001
- **总列数**: 49
- **内存使用**: 276.84 MB

### 数据类型分布
- **float64**: 27 列
- **int64**: 14 列
- **object**: 8 列

## 2. 目标变量分析

### 目标变量: 是否购买理财

- **未知**: 332,172 (99.45%)
- **1**: 1,829 (0.55%)

### 关键发现
- 数据存在严重的类别不平衡问题
- 已购买理财客户占比仅 0.00%
- 大量客户标签为"未知"，适合使用PU学习方法

## 3. 数值型特征分析

- **数值型特征数量**: 40
- **主要特征类型**: 存贷款金额、交易笔数、产品持有数量等

## 4. 分类型特征分析

- **分类型特征数量**: 17
- **主要特征类型**: 各类业务使用情况、客户状态标识等

## 5. 数据质量分析

### 缺失值情况
- **有缺失值的列数**: 24

### 缺失值最严重的特征
- **大额存单年日均余额**: 333,569 (99.87%)
- **国内保函-余额**: 332,595 (99.58%)
- **银承贴现-余额**: 332,483 (99.55%)
- **银承贴现-本年发生额**: 332,483 (99.55%)
- **结构性存款年日均余额**: 332,250 (99.48%)
- **即期结售汇-本年交易量折美元**: 330,779 (99.04%)
- **国际结算-本年交易量折美元**: 330,289 (98.89%)
- **线上供应链-本年发生额**: 329,129 (98.54%)
- **线上供应链-余额**: 326,439 (97.74%)
- **近12月代扣公积金金额**: 323,501 (96.86%)

## 6. 关键发现与建议

### 数据特点
1. **样本不平衡**: 正样本比例极低，需要使用专门的不平衡学习技术
2. **高缺失率**: 部分特征缺失率超过90%，需要谨慎处理
3. **特征丰富**: 涵盖存贷款、交易行为、产品使用等多个维度

### 建议
1. **数据预处理**:
   - 删除缺失率过高的特征（>95%）
   - 对重要特征进行缺失值填充
   - 处理异常值和重复值

2. **特征工程**:
   - 对分类特征进行编码
   - 考虑特征组合和衍生特征
   - 进行特征选择和降维

3. **模型策略**:
   - 使用PU学习处理未标注样本
   - 采用集成学习处理不平衡问题
   - 重点关注召回率和F1分数

## 7. 输出文件清单

### 图表文件
- 目标变量分布图
- 数值特征分布图（多页）
- 数值特征箱线图（多页）
- 分类特征分布图（多页）
- 缺失值热力图
- 缺失值柱状图
- 特征相关性热力图

### 数据文件
- 数值特征统计信息CSV
- 特征相关性矩阵CSV
- 分析结果JSON

---
*报告生成时间: 2025-06-08 16:15:49*
