2025-06-08 17:16:31,170 - INFO - ================================================================================
2025-06-08 17:16:31,172 - INFO - 对公理财客户分析项目启动
2025-06-08 17:16:31,172 - INFO - ================================================================================
2025-06-08 17:17:07,073 - INFO - 配置已保存到: run_config_20250608_171707.json
2025-06-08 17:17:07,073 - INFO - 开始执行所有模块
2025-06-08 17:17:07,073 - INFO - 开始执行模块1: 源数据探索性分析
2025-06-08 17:17:07,077 - INFO - 执行文件: 01_data_exploration.py
2025-06-08 17:17:35,703 - INFO - 模块1执行成功，耗时: 28.62秒
2025-06-08 17:17:35,703 - INFO - 输出: ʼ̽Է...
================================================================================
ڼ...
[OK] ݼسɹ: 334,001  x 49 

============================================================
1. ݻϢ
============================================================
״: (334001, 49)
: 334,001
: 49
ڴʹ: 276.84 MB
ͷֲ:
  float64: 27 
  int64: 14 
  object: 8 

============================================================
2. Ŀ
============================================================
Ŀ: Ƿ
ȡֲֵ:
  δ֪: 332,172 (99.45%)
  1: 1,829 (0.55%)
[OK] Ŀֲͼѱ: output/01_EDA\20250608_171708_Ŀֲ.png

============================================================
3. ֵ
============================================================
ֵ: 40
[OK] ֵͳϢѱ: output/01_EDA\20250608_171708_ֵͳ.csv
[OK] ֲֵͼ1ҳѱ: output/01_EDA\20250608_171708_ֲֵ_1ҳ.png
[OK] ֲֵͼ2ҳѱ: output/01_EDA\20250608_171708_ֲֵ_2ҳ.png
[OK] ֲֵͼ3ҳѱ: output/01_EDA\20250608_171708_ֲֵ_3ҳ.png
[OK] ֲֵͼ4ҳѱ: output/01_EDA\20250608_171708_ֲֵ_4ҳ.png
[OK] ֵͼ1ҳѱ: output/01_EDA\20250608_171708_ֵͼ_1ҳ.png
[OK] ֵͼ2ҳѱ: output/01_EDA\20250608_171708_ֵͼ_2ҳ.png
[OK] ֵͼ3ҳѱ: output/01_EDA\20250608_171708_ֵͼ_3ҳ.png
[OK] ֵͼ4ҳѱ: output/01_EDA\20250608_171708_ֵͼ_4ҳ.png
[OK] ֵͼ5ҳѱ: output/01_EDA\20250608_171708_ֵͼ_5ҳ.png

============================================================
4. 
============================================================
ǿʶĲ: 17
: 0
: 17

Ƿ:
  Ψһֵ: 3
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 255,556 (76.51%)
    : 72,993 (21.85%)
    δ˻: 5,452 (1.63%)

Ƿ¿:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 296,240 (88.69%)
    : 37,761 (11.31%)

Ƿֵ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 320,220 (95.87%)
    : 13,781 (4.13%)

˰-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 240,843 (72.11%)
    1: 93,158 (27.89%)

-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 302,615 (90.60%)
    1: 31,386 (9.40%)

Ƿд:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 322,695 (96.61%)
    : 11,306 (3.39%)

-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 328,076 (98.23%)
    1: 5,925 (1.77%)

ǷԾͻ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    1: 193,259 (57.86%)
    0: 140,742 (42.14%)

-ǷǩԼ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 301,931 (90.40%)
    1: 32,070 (9.60%)

ǷЧ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 234,222 (70.13%)
    : 99,779 (29.87%)

ֽ-ֱ-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 331,172 (99.15%)
    1: 2,829 (0.85%)

ɷ-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 328,531 (98.36%)
    1: 5,470 (1.64%)

۹-Ƿʹ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 320,971 (96.10%)
    1: 13,030 (3.90%)

Ƿͬҵ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 333,554 (99.87%)
    1: 447 (0.13%)

Ƿͻ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    : 217,689 (65.18%)
    : 116,312 (34.82%)

˰-ǷǩԼ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    0: 297,489 (89.07%)
    1: 36,512 (10.93%)

Ƿ:
  Ψһֵ: 2
  ȱʧֵ: 0 (0.00%)
  ȡֲֵ:
    δ֪: 332,172 (99.45%)
    1: 1,829 (0.55%)
[OK] ֲͼ1ҳѱ: output/01_EDA\20250608_171708_ֲ_1ҳ.png
[OK] ֲͼ2ҳѱ: output/01_EDA\20250608_171708_ֲ_2ҳ.png
[OK] ֲͼ3ҳѱ: output/01_EDA\20250608_171708_ֲ_3ҳ.png

============================================================
5. ȱʧֵ
============================================================
ȱʧֵ: 24
ȱʧֵصǰ20:
  浥վ: 333,569 (99.87%)
  ڱ-: 332,595 (99.58%)
  -: 332,483 (99.55%)
  -귢: 332,483 (99.55%)
  ṹԴվ: 332,250 (99.48%)
  ڽۻ-꽻Ԫ: 330,779 (99.04%)
  ʽ-꽻Ԫ: 330,289 (98.89%)
  ϹӦ-귢: 329,129 (98.54%)
  ϹӦ-: 326,439 (97.74%)
  12´۹: 323,501 (96.86%)
  12´۹: 323,501 (96.86%)
  귢: 314,011 (94.01%)
  : 314,011 (94.01%)
  12´ʽ: 307,847 (92.17%)
  ڴվ: 307,453 (92.05%)
  վ: 307,453 (92.05%)
  12´ʱ: 299,912 (89.79%)
  12»ת: 141,700 (42.43%)
  12»ת: 141,700 (42.43%)
  12»ת: 141,064 (42.23%)
[OK] ȱʧֵͼѱ: output/01_EDA\20250608_171708_ȱʧֵͼ.png
[OK] ȱʧֵ״ͼѱ: output/01_EDA\20250608_171708_ȱʧֵ״ͼ.png

============================================================
6. Է
============================================================
[OK] Ծѱ: output/01_EDA\20250608_171708_Ծ.csv
[OK] ͼѱ: output/01_EDA\20250608_171708_ͼ.png

 17 Ը (|ϵ| > 0.8):
  12»ת <-> 12»ڽܽ: 1.000
  12»ת <-> 12»ڽܽ: 1.000
  12»ת <-> 12»ת: 1.000
  һ귢 <-> 귢: 1.000
  һ <-> : 1.000
  ʽ-꽻Ԫ <-> ϹӦ-귢: 0.998
  ʽ-꽻Ԫ <-> ϹӦ-: 0.995
  ڴվ <-> վ: 0.994
  12´۹ <-> 12´ʽ: 0.984
  վ <-> : 0.963
  ڴվ <-> : 0.961
  վ <-> һ: 0.961
  ڴվ <-> һ: 0.955
  ¾ױ <-> 12»ڽܱ: 0.910
  12»ת <-> 12»ڽܱ: 0.813
  ϹӦ- <-> ϹӦ-귢: 0.808
  - <-> -귢: 0.801

============================================================
7. ɷժҪ
============================================================
[OK] ժҪѱ: output/01_EDA\20250608_171708_̽.md
[OK] JSONѱ: output/01_EDA\20250608_171708_.json

================================================================================
̽Էɣ
нѱ浽: output/01_EDA
================================================================================

[OK] ģ1ִгɹ

2025-06-08 17:17:35,708 - INFO - 开始执行模块2: 数据预处理与特征工程
2025-06-08 17:17:35,708 - INFO - 执行文件: 02_data_preprocessing.py
2025-06-08 17:17:53,260 - INFO - 模块2执行成功，耗时: 17.55秒
2025-06-08 17:17:53,260 - INFO - 输出: ʼԤ...
================================================================================
ڼ...
[OK] ݼسɹ: 334,001  x 49 

============================================================
1. ȱʧֵ
============================================================
ȱʧʳ 95.0% : 11
ɾȱʧ:
  - 浥վ: 99.87%
  - ṹԴվ: 99.48%
  - ʽ-꽻Ԫ: 98.89%
  - ڽۻ-꽻Ԫ: 99.04%
  - ϹӦ-: 97.74%
  - ϹӦ-귢: 98.54%
  - ڱ-: 99.58%
  - -: 99.55%
  - -귢: 99.55%
  - 12´۹: 96.86%
  - 12´۹: 96.86%

ʣȱʧֵ: 13
  - ׸˻:  '1999/6/21 0:00'  1 ȱʧֵ
  - ¾ױ: 0 121279 ȱʧֵ
  - ڴվ: 0 307453 ȱʧֵ
  - վ: 0 307453 ȱʧֵ
  - ܶվ: 0 5452 ȱʧֵ
  - 귢: 0 314011 ȱʧֵ
  - : 0 314011 ȱʧֵ
  - 12»ת: 0 141700 ȱʧֵ
  - 12»ת: 0 141700 ȱʧֵ
  - 12»ת: 0 141064 ȱʧֵ
  - 12»ת: 0 141064 ȱʧֵ
  - 12´ʽ: 0 307847 ȱʧֵ
  - 12´ʱ: 0 299912 ȱʧֵ

[OK] ȱʧֵ
  ɾ: 11
  : 13

============================================================
2. ظֵ
============================================================
[OK] δظ

============================================================
3. 
============================================================
Ҫķ: 16
  - ֽ-ֱ-Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - ۹-Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - ˰-ǷǩԼ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - ˰-Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - ɷ-Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - -Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - -ǷǩԼ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - -Ƿʹ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - Ƿ: 3 Ψһֵ
     One-Hot Encoding:  2 
  - Ƿд: 2 Ψһֵ
     Label Encoding: ['' '']
  - Ƿ¿: 2 Ψһֵ
     Label Encoding: ['' '']
  - ǷЧ: 2 Ψһֵ
     Label Encoding: ['' '']
  - Ƿֵ: 2 Ψһֵ
     Label Encoding: ['' '']
  - ǷԾͻ: 2 Ψһֵ
     Label Encoding: ['0' '1']
  - Ƿͻ: 2 Ψһֵ
     Label Encoding: ['' '']
  - Ƿͬҵ: 2 Ψһֵ
     Label Encoding: ['0' '1']
[OK] 

============================================================
4. 
============================================================
: ׸˻
  [OK] : , 
  [OK] ɾԭʼ: ׸˻
[OK] 

============================================================
5. 쳣ֵȫݱ棩
============================================================
쳣ֵֵ: 36
  в: 15
  н: 10
: ֵֻضϣɾκ
  - EVA: ض 2120 쳣ֵ (0.63%) [ֵ]
  - ¾ױ: ض 334 쳣ֵ (0.10%) []
  - ڴվ: ض 1669 쳣ֵ (0.50%) [ֵ]
  - վ: ض 1492 쳣ֵ (0.45%) [ֵ]
  - ܶվ: ض 1670 쳣ֵ (0.50%) [ֵ]
  - вƷ: ض 457 쳣ֵ (0.14%) [ֵ]
  - ҵ-¼: ض 334 쳣ֵ (0.10%) []
  - һ: ض 1202 쳣ֵ (0.36%) [ֵ]
  - һ귢: ض 1205 쳣ֵ (0.36%) [ֵ]
  - 귢: ض 1199 쳣ֵ (0.36%) [ֵ]
  - : ض 1159 쳣ֵ (0.35%) [ֵ]
  - 12»ת: ض 334 쳣ֵ (0.10%) []
  - 12»ת: ض 334 쳣ֵ (0.10%) []
  - 12»ת: ض 334 쳣ֵ (0.10%) []
  - 12»ת: ض 334 쳣ֵ (0.10%) []
  - 12»ڽܽ: ض 334 쳣ֵ (0.10%) []
  - 12»ڽܱ: ض 334 쳣ֵ (0.10%) []
  - 12´ʽ: ض 334 쳣ֵ (0.10%) []
  - 12´ʱ: ض 330 쳣ֵ (0.10%) []
  - ֽ-ֱ-Ƿʹ: 쳣ֵ⣨
  - ۹-Ƿʹ: 쳣ֵ⣨
  - ˰-ǷǩԼ: 쳣ֵ⣨
  - ˰-Ƿʹ: 쳣ֵ⣨
  - ɷ-Ƿʹ: 쳣ֵ⣨
  - -Ƿʹ: 쳣ֵ⣨
  - -ǷǩԼ: 쳣ֵ⣨
  - -Ƿʹ: 쳣ֵ⣨
  - Ƿд: 쳣ֵ⣨
  - Ƿ¿: 쳣ֵ⣨
  - ǷЧ: 쳣ֵ⣨
  - Ƿֵ: 쳣ֵ⣨
  - ǷԾͻ: 쳣ֵ⣨
  - Ƿͻ: 쳣ֵ⣨
  - Ƿͬҵ: 쳣ֵ⣨
  - : ض 3177 쳣ֵ (0.95%) [ֵ]
  - : ض 1216 쳣ֵ (0.36%) [ֵ]
[OK] ֲ: 334,001 
[OK] 쳣ֵɣ 21 ضϣδɾݣ

============================================================
6. 
============================================================
 4 Ը (ϵ > 0.99)
ɾ 3 :
  - 12»ת
  - 12»ת
  - 
[OK] 

============================================================
8. 洦
============================================================
[OK] Ҫļѱ: processed_data.csv
[OK] ļѱ: output/02_processing\processed_data_backup_20250608_171738.csv
[OK] ֵͳѱ: output/02_processing\20250608_171738_ֵͳ.csv
[OK] ͳϢѱ: output/02_processing\20250608_171738_ݴͳ.json

============================================================
9. ɶԱͼ
============================================================
[OK] ״Աͼѱ: output/02_processing\20250608_171738_״Ա.png
[OK] ȱʧֵԱͼѱ: output/02_processing\20250608_171738_ȱʧֵԱ.png
[OK] ֲԱͼѱ: output/02_processing\20250608_171738_ֲԱ.png
[OK] Աͼ

============================================================
10. ɴ
============================================================
[OK] ݴѱ: output/02_processing\20250608_171738_ݴ.md

================================================================================
Ԥɣ
ԭʼ: 334,001  x 49 
: 334,001  x 37 
нѱ浽: output/02_processing
================================================================================

[OK] ģ2ִгɹ

2025-06-08 17:17:53,264 - INFO - 开始执行模块3: PU学习与特征选择
2025-06-08 17:17:53,264 - INFO - 执行文件: 03_pu_learning_feature_selection.py
2025-06-08 17:18:33,866 - INFO - 模块3执行成功，耗时: 40.60秒
2025-06-08 17:18:33,866 - INFO - 输出: ʼPUѧϰѡ...
================================================================================
ڼش...
[OK] ݼسɹ: 334,001  x 37 
Ŀֲ:
  δ֪: 332,172
  1: 1,829

============================================================
1. ׼PUѧϰ
============================================================
: 1,829
δ֪: 332,172
: 35

============================================================
2. 
============================================================
ʹ÷: spy

ʹSpyɸ...
: 274
Ԥֵ: 0.0017
ɵĸ: 235993

עݼ:
  : 1,829
  : 235,993
  ܼ: 237,822
  : 1:129.03

============================================================
3. ֲ
============================================================
[OK] ֲԱͼѱ: output/03_feature_selection\20250608_171759_ֲԱ.png

ͳƶԱ:
  ڴվ:
    ֵ: 412818.0476, ׼: 1669329.6129
    ֵ: 28497.5061, ׼: 389126.7091
    ֵ: 0.9310
  ܶվ:
    ֵ: 1404176.7179, ׼: 3451619.3581
    ֵ: 24453.6592, ׼: 500155.1905
    ֵ: 0.9826
  һ:
    ֵ: 343360.1733, ׼: 1618399.0669
    ֵ: 30924.7109, ׼: 461993.8852
    ֵ: 0.9099
  ¾ױ:
    ֵ: 39.3576, ׼: 69.7275
    ֵ: 1.3006, ׼: 7.0532
    ֵ: 0.9670
  12»ת:
    ֵ: 330.4112, ׼: 582.9577
    ֵ: 9.4858, ׼: 53.1601
    ֵ: 0.9713
  12»ת:
    ֵ: 179.5429, ׼: 367.8007
    ֵ: 7.7278, ׼: 42.2950
    ֵ: 0.9570

[OK] ͳƶԱȽѱ: output/03_feature_selection\20250608_171759_ͳƶԱ.csv

============================================================
4. IVֵ㣨ɸ
============================================================
IVֵɣǰ20:
   1. ܶվ: 5.7867
   2. EVA: 5.0153
   3. 12»ڽܽ: 4.8065
   4. 12»ڽܱ: 3.8395
   5. 12»ת: 3.6866
   6. 12»ת: 3.4579
   7. ¾ױ: 3.0207
   8. ҵ-¼: 2.0195
   9. вƷ: 1.3794
  10. Ƿ_: 1.0355
  11. : 0.1452
  12. Ƿ_δ˻: 0.0005
  13. ڴվ: 0.0000
  14. վ: 0.0000
  15. һ: 0.0000
  16. һ귢: 0.0000
  17. 귢: 0.0000
  18. : 0.0000
  19. 12´ʽ: 0.0000
  20. 12´ʱ: 0.0000

IVֵɸѡ:
  ֵ: 0.02
  ɸѡǰ: 35
  ɸѡ: 11
  ɾ: 24

============================================================
5. Ҫѡ
============================================================

ʹLightGBMҪ...
[OK] LightGBMҪԼ

ʹSHAPҪ...
[OK] SHAPҪԼ

ʹҪԼҪ...
[OK] ҪԼ

============================================================
6. ۺ
============================================================
ۺǰ30:
   1. ҵ-¼
      ۺ: 1.20
      LGB: 0.3375, SHAP: 0.4377, PERM: 0.4002
   2. ܶվ
      ۺ: 2.20
      LGB: 0.1397, SHAP: 0.2025, PERM: 0.4813
   3. 12»ڽܽ
      ۺ: 2.60
      LGB: 0.2475, SHAP: 0.1288, PERM: 0.0784
   4. EVA
      ۺ: 4.80
      LGB: 0.0530, SHAP: 0.0530, PERM: 0.0063
   5. 12»ڽܱ
      ۺ: 5.40
      LGB: 0.0388, SHAP: 0.0443, PERM: 0.0144
   6. 
      ۺ: 5.40
      LGB: 0.0658, SHAP: 0.0419, PERM: 0.0017
   7. 12»ת
      ۺ: 6.80
      LGB: 0.0381, SHAP: 0.0276, PERM: 0.0169
   8. вƷ
      ۺ: 8.40
      LGB: 0.0220, SHAP: 0.0279, PERM: 0.0006
   9. 12»ת
      ۺ: 9.00
      LGB: 0.0330, SHAP: 0.0099, PERM: 0.0002
  10. ¾ױ
      ۺ: 9.20
      LGB: 0.0228, SHAP: 0.0236, PERM: 0.0000
  11. Ƿ_
      ۺ: 11.00
      LGB: 0.0018, SHAP: 0.0028, PERM: 0.0000

ҪԶԱͼ...
[OK] ҪԶԱͼѱ: output/03_feature_selection\20250608_171759_ҪԶԱ.png

SHAPϸͼ...
SHAPٲͼʱ: waterfall() got multiple values for argument 'show'
[OK] SHAPͼѱ: output/03_feature_selection\20250608_171759_SHAPҪԻ.png
[OK] SHAPֲͼѱ: output/03_feature_selection\20250608_171759_SHAPӰֲ.png

============================================================
7. 
============================================================
[OK] עѱ: labeled_samples.csv
[OK] ļѱ: output/03_feature_selection\labeled_samples_backup_20250608_171759.csv
[OK] бѱ: final_features.json
[OK] бѱ: final_features_backup_20250608_171759.json
[OK] ϸѡѱ: output/03_feature_selection\20250608_171759_ѡϸ.json

============================================================
8. ɷ
============================================================
[OK] PUѧϰѡ񱨸ѱ: output/03_feature_selection\20250608_171759_PUѧϰѡ񱨸.md

================================================================================
PUѧϰѡɣ
ѡ: 11
ע: 237,822
нѱ浽: output/03_feature_selection
================================================================================

[OK] ģ3ִгɹ

2025-06-08 17:18:33,868 - INFO - 开始执行模块4: 潜在客户预测模型
2025-06-08 17:18:33,868 - INFO - 执行文件: 04_customer_prediction.py
2025-06-08 17:19:56,460 - INFO - 模块4执行成功，耗时: 82.59秒
2025-06-08 17:19:56,461 - INFO - 输出: ʼǱڿͻԤģѵ...
================================================================================
ڼ...
[OK] עݼسɹ: 237,822  x 39 
[OK] бسɹ: 11 
[OK] δ֪ͻݼسɹ: 332,172 
עݷֲ:
  0: 235,993
  1: 1,829

============================================================
1. ׼ѵ
============================================================
״: (237822, 11)
ǩֲ: Counter({0: 235993, 1: 1829})

============================================================
2. EasyEnsembleģ
============================================================
ò:
  ӷ: lightgbm
  ӷ: 20
  : 1:1 (: 1.00)
[OK] EasyEnsembleģʹ

============================================================
3. ģѵ
============================================================
ѵС: 166,475
ԼС: 71,347
ѵǩֲ: Counter({0: 165195, 1: 1280})
Լǩֲ: Counter({0: 70798, 1: 549})

ʼѵEasyEnsembleģ...
[OK] ģѵ

ģ:
  AUC: 0.9953
  F1 Score: 0.5455
  G-Mean: 0.9711
  MCC: 0.5997
  Sensitivity (Recall): 0.9545
  Specificity: 0.9880

ϸ౨:
              precision    recall  f1-score   support

           0       1.00      0.99      0.99     70798
           1       0.38      0.95      0.55       549

    accuracy                           0.99     71347
   macro avg       0.69      0.97      0.77     71347
weighted avg       0.99      0.99      0.99     71347


============================================================
4. ֲ㽻֤
============================================================
ִ 5 ۽֤...
   1/5...
   2/5...
   3/5...
   4/5...
   5/5...

֤:
  AUC: 0.9948  0.0014
  F1: 0.5483  0.0294
  G_MEAN: 0.9728  0.0054
  MCC: 0.6026  0.0222

============================================================
5. Ԥδ֪ͻ
============================================================
Ԥ 332,172 δ֪ͻ...
Ԥͳ:
  ԤΪ: 94,754
  ԤΪ: 237,418
  ͳ:
    ֵ: 0.2985
    ׼: 0.4225
    Сֵ: 0.0033
    ֵ: 0.9975
    λ: 0.0211
[OK] δ֪ͻԤ

============================================================
6. ͼ
============================================================
[OK] ģͼѱ: output/04_prediction\20250608_171837_ģ.png

Ԥͼ...
[OK] Ԥͼѱ: output/04_prediction\20250608_171837_Ԥ.png

============================================================
7. 
============================================================
[OK] Ԥѱ: prediction_scores.csv
[OK] ļѱ: output/04_prediction\prediction_scores_backup_20250608_171837.csv
[OK] ģѱ: lightgbm_model.pkl
[OK] ѱ: output/04_prediction\20250608_171837_ģ.json
[OK] Ԥѱ: output/04_prediction\20250608_171837_Ԥ.csv (332,172 ͻ)

============================================================
8. ɷ
============================================================
[OK] Ԥģͷѱ: output/04_prediction\20250608_171837_Ԥģͷ.md

================================================================================
ǱڿͻԤģɣ
ģ - AUC: 0.9953, F1: 0.5455
Ԥͻ: 332,172
нѱ浽: output/04_prediction
================================================================================

[OK] ģ4ִгɹ

2025-06-08 17:19:56,464 - INFO - 开始执行模块5: 客户聚类分析
2025-06-08 17:19:56,465 - INFO - 执行文件: 05_customer_clustering.py
2025-06-08 17:30:28,035 - ERROR - 模块5执行失败，返回码: 1
2025-06-08 17:30:28,035 - ERROR - 错误信息:   File "E:\software\anaconda3\Lib\site-packages\joblib\externals\loky\backend\context.py", line 257, in _count_physical_cores
    cpu_info = subprocess.run(
               ^^^^^^^^^^^^^^^
  File "E:\software\anaconda3\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\software\anaconda3\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "E:\software\anaconda3\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Traceback (most recent call last):
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 1110, in <module>
    sys.exit(main())
             ^^^^^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 1100, in main
    success = clusterer.run_clustering_analysis()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 1056, in run_clustering_analysis
    self.perform_dbscan_clustering()
  File "E:\2025\Ӫר\ƿͻȺ\060811\05_customer_clustering.py", line 272, in perform_dbscan_clustering
    final_labels = final_dbscan.fit_predict(self.X_scaled)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\software\anaconda3\Lib\site-packages\sklearn\cluster\_dbscan.py", line 474, in fit_predict
    self.fit(X, sample_weight=sample_weight)
  File "E:\software\anaconda3\Lib\site-packages\sklearn\base.py", line 1473, in wrapper
    return fit_method(estimator, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\software\anaconda3\Lib\site-packages\sklearn\cluster\_dbscan.py", line 422, in fit
    neighborhoods = neighbors_model.radius_neighbors(X, return_distance=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\software\anaconda3\Lib\site-packages\sklearn\neighbors\_base.py", line 1260, in radius_neighbors
    chunked_results = Parallel(n_jobs, prefer="threads")(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\software\anaconda3\Lib\site-packages\sklearn\utils\parallel.py", line 74, in __call__
    return super().__call__(iterable_with_config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\software\anaconda3\Lib\site-packages\joblib\parallel.py", line 1918, in __call__
    return output if self.return_generator else list(output)
                                                ^^^^^^^^^^^^
  File "E:\software\anaconda3\Lib\site-packages\joblib\parallel.py", line 1847, in _get_sequential_output
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "E:\software\anaconda3\Lib\site-packages\sklearn\utils\parallel.py", line 136, in __call__
    return self.function(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\software\anaconda3\Lib\site-packages\sklearn\neighbors\_base.py", line 1039, in _tree_query_radius_parallel_helper
    return tree.query_radius(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "sklearn\\neighbors\\_binary_tree.pxi", line 1431, in sklearn.neighbors._kd_tree.BinaryTree64.query_radius
  File "sklearn\\neighbors\\_binary_tree.pxi", line 1384, in sklearn.neighbors._kd_tree.BinaryTree64.query_radius
MemoryError

2025-06-08 17:30:28,035 - INFO - 输出: ʼͻ...
================================================================================
ڼ...
[OK] ݼسɹ: 334,001  x 37 
[OK] бسɹ: 11 
ݷֲ:
  δ֪: 332,172
  1: 1,829

============================================================
1. ׼
============================================================
״: (334001, 11)
ݱ׼...
[OK] ׼
  ׼ֵ: [ 2.17842390e-17 -1.76996942e-17 -2.45072689e-17 -1.39555281e-17
  1.08921195e-17]...
  ׼׼: [1. 1. 1. 1. 1.]...

============================================================
2. K-Means
============================================================
ѰKֵ (Χ: 2-9)...
  ϴʹ 10,000 ϵ...
   K=2...
    ϵ: 0.8072
   K=3...
    ϵ: 0.4198
   K=4...
    ϵ: 0.4271
   K=5...
    ϵ: 0.4570
   K=6...
    ϵ: 0.3945
   K=7...
    ϵ: 0.3960
   K=8...
    ϵ: 0.4040
   K=9...
    ϵ: 0.4141

Kֵ: 2 (ϵ: 0.8072)
ʹK=2վ...

K-Means:
   0: 325,576 ͻ (97.48%)
   1: 8,425 ͻ (2.52%)
[OK] K-Means

============================================================
3. DBSCANŻ
============================================================
ѰŲ...
  epsΧ: 0.3-1.0
  min_samplesΧ: 5-15
  Ż: 10,000
  ֵͣ: 0.3
  ʹ 10,000 вŻ
 24 ֲ...
  : 10/24 (ǰϵ: 0.2227)
  : 20/24 (ǰϵ: 0.2342)
  ͣҵϵ 0.4267 > 0.3

Ų: eps=1.00, min_samples=5
ϵ: 0.4267
ʹŲȫݽо...

