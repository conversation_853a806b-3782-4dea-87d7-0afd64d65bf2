# PU学习与特征选择报告

## 报告信息
- **生成时间**: 2025年06月08日 12:19:32
- **数据文件**: processed_data.csv
- **PU学习方法**: spy

## 1. 数据概况

### 原始数据
- **总样本数**: 232,314
- **正样本数**: 374
- **未知样本数**: 231,940
- **原始特征数**: 40

### 标注数据
- **正样本数**: 374
- **负样本数**: 173,666
- **正负样本比例**: 1:464.35

## 2. PU学习方法

### SPY方法

- **间谍比例**: 0.15
- **方法描述**: 从正样本中随机选择15.0%作为间谍，混入未知样本中训练分类器

## 3. 特征选择流程

### 第一步：IV值粗筛
- **IV阈值**: 0.02
- **筛选前特征数**: 40
- **筛选后特征数**: 通过IV值筛选

### 第二步：多方法特征重要性评估
1. **LightGBM特征重要性**: 基于梯度提升决策树的特征重要性
2. **SHAP值**: 基于博弈论的特征贡献度分析
3. **排列重要性**: 基于特征打乱后性能下降的重要性评估

### 第三步：综合排名
- **最终特征数**: 16
- **排名策略**: 加权综合排名（LGB:40%, SHAP:40%, PERM:20%）

## 4. 最终选择的特征

### 前20个重要特征

 1. **存款总额年日均**
   - LightGBM: 0.1559
   - SHAP: 0.0625
   - 排列重要性: 0.4714

 2. **企业网银-本年登录次数**
   - LightGBM: 0.2760
   - SHAP: 0.0625
   - 排列重要性: 0.4314

 3. **存贷款EVA**
   - LightGBM: 0.0523
   - SHAP: 0.0625
   - 排列重要性: 0.0400

 4. **近12月活期转出笔数**
   - LightGBM: 0.0351
   - SHAP: 0.0625
   - 排列重要性: 0.0128

 5. **近12月活期转出金额**
   - LightGBM: 0.0336
   - SHAP: 0.0625
   - 排列重要性: 0.0078

 6. **近12月活期转入金额**
   - LightGBM: 0.1321
   - SHAP: 0.0625
   - 排列重要性: -0.0032

 7. **平均单笔交易金额**
   - LightGBM: 0.0669
   - SHAP: 0.0625
   - 排列重要性: 0.0483

 8. **当年月均交易笔数**
   - LightGBM: 0.0285
   - SHAP: 0.0625
   - 排列重要性: -0.0065

 9. **近12月活期交易总笔数**
   - LightGBM: 0.0353
   - SHAP: 0.0625
   - 排列重要性: 0.0031

10. **持有产品总数**
   - LightGBM: 0.0042
   - SHAP: 0.0625
   - 排列重要性: 0.0000

11. **近12月活期交易总金额**
   - LightGBM: 0.0327
   - SHAP: 0.0625
   - 排列重要性: -0.0014

12. **近12月活期转入笔数**
   - LightGBM: 0.0283
   - SHAP: 0.0625
   - 排列重要性: 0.0031

13. **开户距今天数**
   - LightGBM: 0.0771
   - SHAP: 0.0625
   - 排列重要性: -0.0033

14. **近12月代发工资笔数**
   - LightGBM: 0.0060
   - SHAP: 0.0625
   - 排列重要性: -0.0029

15. **客户价值评分**
   - LightGBM: 0.0323
   - SHAP: 0.0625
   - 排列重要性: -0.0028

16. **是否销户_是**
   - LightGBM: 0.0039
   - SHAP: 0.0625
   - 排列重要性: 0.0022

## 5. 关键发现

### 数据质量
- PU学习成功生成了173,666个负样本
- 正负样本在关键特征上表现出明显差异
- 特征选择有效降低了维度，提高了模型可解释性

### 特征特点
- 存款相关特征在区分正负样本中起重要作用
- 交易行为特征显示出较强的预测能力
- 业务使用情况特征有助于客户分类

## 6. 输出文件

### 主要文件
- `labeled_samples.csv`: 标注后的训练数据
- `final_features.json`: 最终选择的特征列表

### 分析文件
- 正负样本分布对比图
- 特征重要性对比图
- 统计对比结果CSV

---
*报告生成时间: 2025-06-08 12:19:32*
